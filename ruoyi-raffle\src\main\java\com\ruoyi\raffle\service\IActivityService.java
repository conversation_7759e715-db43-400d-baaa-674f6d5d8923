package com.ruoyi.raffle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.raffle.domain.Activity;
import com.ruoyi.raffle.domain.dto.request.ActivityPageRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/6 4:57 下午
 */
public interface IActivityService extends IService<Activity> {


    List<Activity> queryActiveList(String tenantId);

    List<Activity> listByCondition(ActivityPageRequest request);

    void activeSave(Activity activity);

    Activity activeQueryById(Long id);

    void activityStatusChange(Long activityId, Integer status);

    List<Activity> queryPublicActivityList(String tenantId);

    Activity queryOneActivity(String tenantId,Long s);
}
