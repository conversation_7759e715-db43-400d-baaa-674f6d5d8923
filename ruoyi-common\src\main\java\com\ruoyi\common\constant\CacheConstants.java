package com.ruoyi.common.constant;

/**
 * 缓存的key 常量
 * 
 * <AUTHOR>
 */
public class CacheConstants
{
    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = "raffle:login_tokens:";

    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = "raffle:captcha_codes:";

    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = "raffle:sys_config:";

    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = "raffle:sys_dict:";

    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = "raffle:repeat_submit:";

    /**
     * 限流 redis key
     */
    public static final String RATE_LIMIT_KEY = "raffle:rate_limit:";

    /**
     * 登录账户密码错误次数 redis key
     */
    public static final String PWD_ERR_CNT_KEY = "raffle:pwd_err_cnt:";


    /**
     * 抽奖
     */
    public static final String LOTTERY_RAFFLE_KEY = "raffle:lottery:%s";

    /**
     * 新年抽红包分享
     */
    public static final String LOTTERY_SHARE_KEY = "raffle:lottery::share:%s";

    /**
     * 新年抽红包完善
     */
    public static final String LOTTERY_COMPLETE_KEY = "raffle:lottery:complete:%s";


}
