package com.ruoyi.raffle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.raffle.domain.ActivityMemberAddress;
import com.ruoyi.raffle.domain.dto.request.ActivityMemberEditAddressRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/6 4:57 下午
 */
public interface IActivityMemberAddressService extends IService<ActivityMemberAddress> {

    List<ActivityMemberAddress> listByCondition();

    boolean editAddress(ActivityMemberEditAddressRequest request);

    ActivityMemberAddress getByMemberId(Long memberId);

}