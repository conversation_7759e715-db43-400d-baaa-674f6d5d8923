<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!--
    黑名单数据访问层映射文件
    提供黑名单相关的SQL映射配置
    
    <AUTHOR>
    @date 2023-12-18
-->
<mapper namespace="com.ruoyi.raffle.mapper.BlackListMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.raffle.domain.BlackList">
        <id column="id" property="id" />
        <result column="open_id" property="openId" />
        <result column="reason" property="reason" />
        <result column="del_flag" property="delFlag" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_date" property="createDate" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_date" property="updateDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, open_id, reason, del_flag, create_user_id, create_user_name,
        create_date, update_user_id, update_user_name, update_date
    </sql>

    <!-- 根据openId查询黑名单记录（包括已删除的记录，因为openId有唯一索引） -->
    <select id="selectByOpenId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List" />
        FROM black_list
        WHERE open_id = #{openId}
        LIMIT 1
    </select>

    <!-- 软删除黑名单记录 -->
    <update id="softDeleteByIds">
        UPDATE black_list
        SET del_flag = 1,
            update_user_id = #{updateUserId},
            update_user_name = #{updateUserName},
            update_date = NOW()
        WHERE
            id IN
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
          AND del_flag = 0
    </update>
</mapper>
