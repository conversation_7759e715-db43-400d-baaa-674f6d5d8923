package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.raffle.domain.LotteryItemConfig;
import com.ruoyi.raffle.domain.dto.LotteryItemConfigInfoDTO;
import com.ruoyi.raffle.mapper.LotteryItemConfigMapper;
import com.ruoyi.raffle.service.ILotteryItemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 抽奖奖品配置表业务逻辑实现类
 * 实现奖品配置相关的业务操作
 * 
 * <AUTHOR>
 * @date 2023-12-19
 */
@Slf4j
@Service
public class LotteryItemConfigServiceImpl extends ServiceImpl<LotteryItemConfigMapper, LotteryItemConfig> 
        implements ILotteryItemConfigService {


    @Override
    public List<LotteryItemConfigInfoDTO> queryItemConfigInfoList(Long activityId) {
        return this.baseMapper.queryItemConfigInfoList(activityId);
    }
}
