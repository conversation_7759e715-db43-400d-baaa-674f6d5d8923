package com.ruoyi.third.party.domain.dto.response.jsp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@NoArgsConstructor
@Data
public class JspUserResponse implements Serializable {

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("result")
    private ResultDTO result;
    @JsonProperty("timestamp")
    private Long timestamp;

    @NoArgsConstructor
    @Data
    public static class ResultDTO {
        @JsonProperty("userId")
        private Long userId;
        @JsonProperty("nickname")
        private String nickname;
        @JsonProperty("info")
        private String info;
        @JsonProperty("gender")
        private Integer gender;
        @JsonProperty("birthday")
        private Object birthday;
        @JsonProperty("avatarImageUrl")
        private String avatarImageUrl;
        @JsonProperty("avatar")
        private String avatar;
        @JsonProperty("hasRealnameAuth")
        private Boolean hasRealnameAuth;
        @JsonProperty("genderVisible")
        private Integer genderVisible;
        @JsonProperty("birthdayVisible")
        private Integer birthdayVisible;
        @JsonProperty("countryCode")
        private String countryCode;
        @JsonProperty("phone")
        private String phone;
        @JsonProperty("newNickName")
        private NewNickNameDTO newNickName;
        @JsonProperty("newIdentity")
        private Object newIdentity;
        @JsonProperty("newInfo")
        private NewInfoDTO newInfo;
        @JsonProperty("newAvatar")
        private NewAvatarDTO newAvatar;
        @JsonProperty("platformUser")
        private Integer platformUser;
        @JsonProperty("isAuthentication")
        private Boolean isAuthentication;
        @JsonProperty("authenticationIntro")
        private String authenticationIntro;
        @JsonProperty("followCount")
        private Integer followCount;
        @JsonProperty("followGroupCount")
        private Integer followGroupCount;
        @JsonProperty("fansCount")
        private Integer fansCount;
        @JsonProperty("provinceId")
        private Object provinceId;
        @JsonProperty("cityId")
        private Object cityId;
        @JsonProperty("province")
        private Object province;
        @JsonProperty("city")
        private Object city;
        @JsonProperty("joinGroup")
        private List<Integer> joinGroup;
        @JsonProperty("isJuniorMode")
        private Boolean isJuniorMode;
        @JsonProperty("hasAlreadySetPwd")
        private Boolean hasAlreadySetPwd;
        @JsonProperty("new")
        private Boolean newX;

        @NoArgsConstructor
        @Data
        public static class NewNickNameDTO {
            @JsonProperty("value")
            private String value;
            @JsonProperty("auditStatus")
            private Integer auditStatus;
            @JsonProperty("auditHint")
            private String auditHint;
        }

        @NoArgsConstructor
        @Data
        public static class NewInfoDTO {
            @JsonProperty("value")
            private String value;
            @JsonProperty("auditStatus")
            private Integer auditStatus;
            @JsonProperty("auditHint")
            private String auditHint;
        }

        @NoArgsConstructor
        @Data
        public static class NewAvatarDTO {
            @JsonProperty("value")
            private String value;
            @JsonProperty("auditStatus")
            private Integer auditStatus;
            @JsonProperty("auditHint")
            private String auditHint;
        }
    }
}
