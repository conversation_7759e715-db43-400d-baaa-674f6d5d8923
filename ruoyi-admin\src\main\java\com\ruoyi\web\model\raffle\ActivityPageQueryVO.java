package com.ruoyi.web.model.raffle;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class ActivityPageQueryVO {


    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动状态：0-活动中、1-已结束
     */
    private Integer status;

    /**
     * 创建开始时间
     */
    private Date createDateStart;

    /**
     * 创建结束时间
     */
    private Date createDateEnd;

    /**
     * 开奖开始时间
     */
    private Date raffleOpenTimeStart;

    /**
     * 开奖结束时间
     */
    private Date raffleOpenTimeEnd;
}
