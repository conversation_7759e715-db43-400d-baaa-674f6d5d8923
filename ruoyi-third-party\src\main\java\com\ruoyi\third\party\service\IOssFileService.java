package com.ruoyi.third.party.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.third.party.domain.OssFile;
import com.ruoyi.third.party.domain.dto.request.FileUploadRequest;
import com.ruoyi.third.party.domain.dto.response.FileUploadResponse;

public interface IOssFileService extends IService<OssFile> {


    FileUploadResponse create(FileUploadRequest fileUploadRequest);

    /**
     * 获取原文件URL
     *
     * @return
     */
    String getOriUrl(String id);


}
