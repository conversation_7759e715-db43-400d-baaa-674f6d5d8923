package com.ruoyi.event.listener;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.event.ActivityOnShelvesEvent;
import com.ruoyi.raffle.domain.Activity;
import com.ruoyi.raffle.domain.RaffleUser;
import com.ruoyi.raffle.service.IActivityService;
import com.ruoyi.raffle.service.IRaffleUserService;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.service.ISysTenantService;
import com.ruoyi.third.party.domain.dto.request.jsp.JspNotificationRequest;
import com.ruoyi.third.party.domain.dto.response.AppletRouteQueryResponse;
import com.ruoyi.third.party.domain.dto.response.jsp.JspNotificationResponse;
import com.ruoyi.third.party.service.IAppletRouteService;
import com.ruoyi.third.party.service.IJspService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ActivityOnShelvesEventListener {

    @Resource
    private IActivityService activityService;
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private IRaffleUserService raffleUserService;
    @Resource
    private ISysTenantService sysTenantService;
    @Resource
    private IAppletRouteService appletRouteService;

    @Resource
    private IJspService jspService;

    @Async
    @EventListener
    public void onEvent(ActivityOnShelvesEvent event) {
        log.info("新活动上架通知事件：{}", JSON.toJSONString(event));

        //  活动信息
        Activity activity = activityService.getById(event.getActivityId());
        //  批量大小
        int batchSize = 200;

        //  分发所有通知
        for (int i = 1; ; i++) {

            LambdaQueryWrapper<RaffleUser> raffleUserQuery = Wrappers.lambdaQuery();
            raffleUserQuery.eq(RaffleUser::getIsSubscribeNewActivityNotification, true);
            raffleUserQuery.eq(RaffleUser::getStatus, 1);
            PageHelper.<RaffleUser>startPage(i, batchSize);
            List<RaffleUser> list = raffleUserService.list(raffleUserQuery);
            PageHelper.clearPage();
            PageInfo<RaffleUser> page = new PageInfo<>(list);

            List<RaffleUser> records = page.getList();

            try {
                batchSendNotification(activity, records);
            } catch (Exception e) {
                log.error("新活动通知异常:", e);
            }
            if (!page.isHasNextPage()) {
                break;
            }

        }


    }

    private void batchSendNotification(Activity activity, List<RaffleUser> raffleUsers) {
        //  按照租户分组
        Map<String, List<RaffleUser>> tenantGroup = raffleUsers.stream().collect(Collectors.groupingBy(RaffleUser::getTenantId));

        //  按照租户分批发送
        tenantGroup.forEach((tenantId, list) -> {

            //  租户信息
            SysTenant sysTenant = sysTenantService.getById(tenantId);

            //  根据不同平台，分发推送信息
            if (Objects.equals(sysTenant.getPlatform(), 1)) {
                //  1、今视频平台
                batchSendNotificationToJsp(activity, list);
            } else {
                //  2、赣云平台

            }
        });


    }


    /**
     * 给今视频发送通知
     *
     * @param activity    活动详情
     * @param raffleUsers 抽奖用户列表
     */
    private void batchSendNotificationToJsp(Activity activity, List<RaffleUser> raffleUsers) {
        //  小程序跳转URL
        AppletRouteQueryResponse.DataDTO appletInfo = getAppletInfo(activity.getId(), activity.getTenantId());
        Map<String, Object> extra = new HashMap<>();
        if (Objects.nonNull(appletInfo) && Objects.nonNull(appletInfo.getApplet())) {
            extra.put("appletName", appletInfo.getApplet().getName());
            extra.put("appletIconUrl", appletInfo.getApplet().getIcon());
        }
        extra.put("messageType", "新活动");
        extra.put("buttonText", "点击查看");

        List<Map<String, Object>> content = new ArrayList<>();
        Map<String, Object> activityName = new HashMap<>();
        activityName.put("key", "活动名称");
        activityName.put("value", activity.getActivityName());
        content.add(activityName);

        Map<String, Object> raffleOpenTime = new HashMap<>();
        raffleOpenTime.put("key", "开奖时间");
        raffleOpenTime.put("value", activity.getRaffleOpenTime());
        content.add(raffleOpenTime);

        Map<String, Object> activityPrizeName = new HashMap<>();
        activityPrizeName.put("key", "奖品名称");
        activityPrizeName.put("value", activity.getActivityPrize());
        content.add(activityPrizeName);

        extra.put("contents", content);


        JspNotificationRequest request = new JspNotificationRequest();
        request.setFromType(0);
        request.setFromId(0L);
        request.setFromName("小程序平台");
        request.setToType(2);
        request.setToIds(raffleUsers.stream().map(e -> Long.valueOf(e.getOpenId())).collect(Collectors.toList()));
        request.setTitle("小程序");
        request.setType(2);
        request.setIntro(activity.getActivityName());
        request.setDetail(activity.getActivityName());
        if (Objects.nonNull(appletInfo)) {
            List<AppletRouteQueryResponse.DataDTO.UrlListDTO> urlList = appletInfo.getUrlList();
            if (Objects.nonNull(urlList)) {
                for (AppletRouteQueryResponse.DataDTO.UrlListDTO urlListDTO : urlList) {
                    if (Objects.equals(activity.getTenantId(), urlListDTO.getPlatformId())) {
                        request.setOuterUrl(addScene(urlListDTO.getUrl()));
                    }
                }
            }
        }
        request.setExtra(JSON.toJSONString(extra));
        request.setAdminUser("18888888888");

        JspNotificationResponse jspNotificationResponse = jspService.sendNotification(request);
        log.info("今视频通知结果:{}", JSON.toJSONString(jspNotificationResponse));
    }

    private String addScene(String url) {
        return StringUtils.hasText(url) ? url + "&scene=103" : url;
    }


    private AppletRouteQueryResponse.DataDTO getAppletInfo(Long activityId, String tenantId) {
        AppletRouteQueryResponse response = appletRouteService.getRouteList(activityId, "pages/index/index");
        if (Objects.nonNull(response)) {
            return response.getData();
        }
        return null;
    }


}
