package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.raffle.domain.LotteryItem;
import com.ruoyi.raffle.domain.LotteryItemCode;
import com.ruoyi.raffle.mapper.LotteryItemMapper;
import com.ruoyi.raffle.service.ILotteryItemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 抽奖奖项表 服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class LotteryItemServiceImpl extends ServiceImpl<LotteryItemMapper, LotteryItem> implements ILotteryItemService {

    @Resource
    private LotteryItemCodeServiceImpl lotteryItemCodeService;

    @Override
    public LotteryItemCode queryCouponCodeByItemId(Long itemId) {
        LotteryItem item = this.getById(itemId);
        if (Objects.isNull(item) || Objects.equals(0, item.getCodeGrantType())) {
            return null;
        }
        return lotteryItemCodeService.getOne(new LambdaQueryWrapper<LotteryItemCode>().eq(LotteryItemCode::getItemId, itemId).eq(LotteryItemCode::getGrantType, 0).last("  ORDER BY RAND() LIMIT 1 "));
    }
}