package com.ruoyi.app.service;


import com.ruoyi.app.model.lottery.LotteryActivityInfoVO;
import com.ruoyi.app.model.lottery.LotteryResultVO;
import com.ruoyi.app.model.lottery.LotteryCompleteWinningVO;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.model.LoginUser;

/**
 * @Author: niedamin
 * @Date: 2023/04/20 17:42
 */
public interface ActivityLotteryService {

    /**
     * 抽奖
     * 
     * @param activityId
     * @param loginUser
     * @return
     */
    R<LotteryResultVO> lottery(Long activityId, LoginUser loginUser);

    /**
     * 活动信息
     * 
     * @param activityId
     * @return
     */
    R<LotteryActivityInfoVO> getActivityInfoById(Long activityId, Long userId);

    /**
     * 分享
     * 
     * @param activityId
     * @param userId
     * @return
     */
    R<Integer> share(Long activityId, Long userId);



    /**
     * 完善中奖信息
     * 
     * @param vo
     * @param userId
     * @return
     */
    R<Boolean> completeWinning(LotteryCompleteWinningVO vo, Long userId);

}
