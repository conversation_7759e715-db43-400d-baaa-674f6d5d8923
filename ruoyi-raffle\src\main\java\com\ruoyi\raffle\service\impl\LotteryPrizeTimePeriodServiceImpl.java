package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.raffle.domain.LotteryPrizeTimePeriod;
import com.ruoyi.raffle.mapper.LotteryPrizeTimePeriodMapper;
import com.ruoyi.raffle.service.ILotteryPrizeTimePeriodService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 抽奖奖品时间控制表业务逻辑实现类
 * 实现奖品时间控制相关的业务操作
 * 
 * <AUTHOR>
 * @date 2023-12-18
 */
@Slf4j
@Service
public class LotteryPrizeTimePeriodServiceImpl extends ServiceImpl<LotteryPrizeTimePeriodMapper, LotteryPrizeTimePeriod> 
        implements ILotteryPrizeTimePeriodService {

}
