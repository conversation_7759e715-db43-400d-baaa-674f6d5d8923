package com.ruoyi.raffle.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 抽奖券码表实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lottery_item_code")
public class LotteryItemCode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Excel(name = "序号", type = Excel.Type.IMPORT)
    private Long id;

    /**
     * 活动id
     */
    @TableField("activity_id")
    private Long activityId;

    /**
     * 奖项id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 券码
     */
    @TableField("code")
    @Excel(name = "券码")
    private String code;

    /**
     * 发放标识 0-未发放 1-已发放
     */
    @TableField("grant_type")
    @Excel(name = "发放标识(0-未发放 1-已发放)", readConverterExp = " 0-未发放 1-已发放" , type = Excel.Type.EXPORT)
    private Integer grantType;

    /**
     * 创建用户id
     */
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 创建用户账号
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 更新用户id
     */
    @TableField("update_user_id")
    private Long updateUserId;

    /**
     * 更新用户账号
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField("update_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}