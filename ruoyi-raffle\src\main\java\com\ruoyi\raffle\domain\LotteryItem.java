package com.ruoyi.raffle.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 抽奖奖项表实体类
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lottery_item")
public class LotteryItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类型: 1-现金 2-今豆 3-物品
     */
    @TableField("type")
    private Integer type;

    /**
     * 奖项名称
     */
    @TableField("name")
    private String name;

    /**
     * 奖项图片
     */
    @TableField("cover")
    private String cover;

    /**
     * 奖项图片url
     */
    @TableField(exist = false)
    private String coverUrl;

    /**
     * 奖品最小值
     */
    @TableField("prize_min")
    private BigDecimal prizeMin;

    /**
     * 奖品最大值
     */
    @TableField("prize_max")
    private BigDecimal prizeMax;

    /**
     * 券码发放类型: 0-不发放 1-发放
     */
    @TableField("code_grant_type")
    private Integer codeGrantType;

    /**
     * 创建用户id
     */
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 创建用户账号
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 更新用户id
     */
    @TableField("update_user_id")
    private Long updateUserId;

    /**
     * 更新用户账号
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField("update_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}