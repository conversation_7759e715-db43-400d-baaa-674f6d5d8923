package com.ruoyi.web.controller.raffle;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.event.ActivityOnShelvesEvent;
import com.ruoyi.raffle.domain.Activity;
import com.ruoyi.raffle.domain.ActivityMember;
import com.ruoyi.raffle.domain.dto.request.ActivityPageRequest;
import com.ruoyi.raffle.domain.dto.request.ActivityWinRequest;
import com.ruoyi.raffle.service.IActivityMemberService;
import com.ruoyi.raffle.service.IActivityService;
import com.ruoyi.third.party.domain.dto.response.AppletRouteQueryResponse;
import com.ruoyi.third.party.service.IAppletRouteService;
import com.ruoyi.third.party.service.IOssFileService;
import com.ruoyi.web.convert.raffle.ActivityConverter;
import com.ruoyi.web.model.raffle.ActivityPageQueryVO;
import com.ruoyi.web.model.raffle.ActivityStatusChangeVO;
import com.ruoyi.web.model.raffle.ActivityVO;
import com.ruoyi.web.model.raffle.MemberStatusChangeVO;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/11/6 4:59 下午
 */

@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping("/raffle/")
public class ActivityController extends BaseController {

    private final IActivityService activityService;
    private final IActivityMemberService activityMemberService;

    private final IOssFileService ossFileService;

    private final IAppletRouteService appletRouteService;

    private final ActivityConverter activityConverter;

    private final ApplicationEventPublisher applicationEventPublisher;


    @ApiOperation("活动列表查询")
    @GetMapping("/activity/list")
    public TableDataInfo queryActiveList(ActivityPageQueryVO vo) {
        // 这里可以获取租户id
        ActivityPageRequest request = activityConverter.convert(vo);
        request.setTenantId(getTenantId());
        startPage();

        request.setSorts(Collections.singletonList(new ActivityPageRequest.Sort("createDate", false)));

        List<Activity> list = activityService.listByCondition(request);
        return getDataTable(list);
    }

    @ApiOperation("活动详情接口")
    @GetMapping("/activity/{id}")
    public R<ActivityVO> getActivity(@PathVariable Long id) {
        Activity activity = activityService.getById(id);

        ActivityVO activityVO = new ActivityVO();
        activityVO.setId(activity.getId());
        activityVO.setActivityName(activity.getActivityName());
        activityVO.setActivityRule(activity.getActivityRule());
        activityVO.setActivityDescription(activity.getActivityDescription());
        activityVO.setActivityNum(activity.getActivityNum());
        activityVO.setRaffleOpenTime(activity.getRaffleOpenTime());
        activityVO.setActivityPromoPic(activity.getActivityPromoPic());
        activityVO.setActivityPromoPicUrl(ossFileService.getOriUrl(activity.getActivityPromoPic()));
        activityVO.setBgColor(activity.getBgColor());
        activityVO.setActivityPrize(activity.getActivityPrize());
        activityVO.setActivityPrizeNum(activity.getActivityPrizeNum());
        activityVO.setActivityPrizeValue(activity.getActivityPrizeValue());
        activityVO.setActivityPrizePic(activity.getActivityPrizePic());
        activityVO.setActivityPrizePicUrl(ossFileService.getOriUrl(activity.getActivityPrizePic()));
        activityVO.setActivityPrizeNeedSent(activity.getActivityPrizeNeedSent());
        activityVO.setActivityUrl(activity.getActivityUrl());
        activityVO.setStatus(activity.getStatus());
        activityVO.setTenantId(activity.getTenantId());
        activityVO.setCreateId(activity.getCreateId());
        activityVO.setCreateName(activity.getCreateName());
        activityVO.setCreateDate(activity.getCreateDate());
        activityVO.setUpdateId(activity.getUpdateId());
        activityVO.setUpdateName(activity.getUpdateName());
        activityVO.setUpdateDate(activity.getUpdateDate());
        activityVO.setIsDeleted(activity.getIsDeleted());
        activityVO.setIsDarkFont(activity.getIsDarkFont());

        return R.ok(activityVO);
    }


    @ApiOperation("活动保存")
    @PostMapping("/activity/save")
    public R<Void> activeSave(@RequestBody Activity activity) {
        activity.setTenantId(getTenantId());
        activityService.activeSave(activity);
        return R.ok();
    }

    @ApiOperation("活动保存")
    @PutMapping("/activity/statusChange")
    public R<Void> activityStatusChange(@RequestBody ActivityStatusChangeVO statusChangeVO) {
        activityService.activityStatusChange(statusChangeVO.getActivityId(), statusChangeVO.getStatus());
        //  发送活动上架事件
        if (Objects.equals(statusChangeVO.getStatus(), 1)) {
            applicationEventPublisher.publishEvent(new ActivityOnShelvesEvent(statusChangeVO.getActivityId()));
        }

        return R.ok();
    }

    @ApiOperation("获取活动链接")
    @GetMapping("/activity/url")
    public R<String> getActivityUrl(@RequestParam(required = false) Long activityId) {

        String tenantId = getTenantId();

        String routeUrl = "";
        String path = "pages/index/index";

        AppletRouteQueryResponse response = appletRouteService.getRouteList(activityId, path);
        AppletRouteQueryResponse.DataDTO data = response.getData();
        List<AppletRouteQueryResponse.DataDTO.UrlListDTO> urlList = data.getUrlList();
        if (!CollectionUtils.isEmpty(urlList)) {
            for (AppletRouteQueryResponse.DataDTO.UrlListDTO dto : urlList) {
                if (Objects.equals(String.valueOf(dto.getPlatformId()), tenantId)) {
                    routeUrl = dto.getUrl();
                    break;
                }
            }
        }
        return R.ok(routeUrl);
    }


    @ApiOperation("已中奖列表查询")
    @GetMapping("/activityWin/list")
    public TableDataInfo queryActiveWinList(ActivityWinRequest request) {
        request.setTenantId(getTenantId());
        startPage();
        List<ActivityMember> list = activityMemberService.getWinMembers(request);
        TableDataInfo dataTable = getDataTable(list);
        dataTable.setRows(list.stream().map(activityConverter::convert).collect(Collectors.toList()));

        return dataTable;
    }

    @ApiOperation("兑奖状态更改")
    @PutMapping("/activityWin/statusChange")
    public R<Void> activeWinStatusChange(@RequestBody MemberStatusChangeVO statusChangeVO) {
        activityMemberService.exchangeStatusChange(statusChangeVO.getMemberId(), statusChangeVO.getStatus());
        return R.ok();
    }


}
