package com.ruoyi.app.controller.lottery;


import com.ruoyi.app.model.lottery.LotteryActivityInfoVO;
import com.ruoyi.app.model.lottery.LotteryResultVO;
import com.ruoyi.app.model.lottery.LotteryCompleteWinningVO;
import com.ruoyi.app.model.lottery.LotteryVO;
import com.ruoyi.app.service.ActivityLotteryService;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.constant.CacheConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.raffle.domain.LotteryActivity;
import com.ruoyi.raffle.service.ILotteryActivityService;
import com.ruoyi.tool.RedisLock;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/1/30 10:45
 */
@RestController
@RequestMapping("/app/lottery")
@Api(value = "活动抽奖", tags = {"活动抽奖"})
@Slf4j
public class ActivityLotteryController {

    @Resource
    private ActivityLotteryService activityLotteryService;

    @Resource
    private RedisLock redisLock;

    @Resource
    private ILotteryActivityService lotteryActivityService;


    @PostMapping
    @ApiOperation(value = "抽奖", notes = "抽奖")
    public R<LotteryResultVO> lottery(@RequestBody @Validated LotteryVO vo) {
        LoginUser loginUser = SecurityUtils.getLoginUser();

        String uuid = UUID.randomUUID().toString();
        String requestKey = String.format(CacheConstants.LOTTERY_RAFFLE_KEY, vo.getActivityId());
        try {
            if (redisLock.lock(requestKey, uuid, 10, TimeUnit.SECONDS)) {
                return activityLotteryService.lottery(vo.getActivityId(), loginUser);
            } else {
                return R.fail(-2, "活动太火爆了，请稍后再试!");
            }
        } finally {
            redisLock.unlockLua(requestKey, uuid);
        }
    }


    @GetMapping("/activity/{id}")
    @ApiOperation(value = "活动信息", notes = "活动信息")
    public R<LotteryActivityInfoVO> getActivityById(@PathVariable(name = "id") Long id) {
        Long userId = SecurityUtils.getUserId();
        return activityLotteryService.getActivityInfoById(id, userId);
    }

    @PostMapping("/share")
    @ApiOperation(value = "活动信息分享", notes = "活动信息分享")
    public R<Integer> share(@RequestBody @Validated LotteryVO vo) {
        Long userId = SecurityUtils.getUserId();
        String uuid = UUID.randomUUID().toString();
        String requestKey = String.format(CacheConstants.LOTTERY_SHARE_KEY, userId);
        try {
            if (redisLock.lock(requestKey, uuid, 10, TimeUnit.SECONDS)) {
                return activityLotteryService.share(vo.getActivityId(), userId);
            } else {
                return R.fail(-2, "活动太火爆了，请稍后再试!");
            }
        } finally {
            redisLock.unlockLua(requestKey, uuid);
        }
    }

    @PostMapping("/complete/winning")
    @ApiOperation(value = "完善中奖信息", notes = "完善中奖信息")
    public R<Boolean> completeWinning(@RequestBody @Validated LotteryCompleteWinningVO vo) {

        Long userId = SecurityUtils.getUserId();

        String uuid = UUID.randomUUID().toString();
        String requestKey = String.format(CacheConstants.LOTTERY_COMPLETE_KEY, vo.getLogId());
        try {
            if (redisLock.lock(requestKey, uuid, 10, TimeUnit.SECONDS)) {
                return activityLotteryService.completeWinning(vo, userId);
            } else {
                return R.fail(-2, "活动太火爆了，请稍后再试!");
            }
        } finally {
            redisLock.unlockLua(requestKey, uuid);
        }
    }

    @ApiOperation("活动公开")
    @GetMapping("/public/activity/{id}")
    @Anonymous
    public R<Map<String, Object>> queryActivityInfo(@PathVariable(name = "id") Long id) {
        LotteryActivity activity = lotteryActivityService.getById(id);
        if (Objects.isNull(activity)) {
            return R.fail(-1, "活动不存在");
        }

        Map<String, Object> map = new HashMap<>(7);
        map.put("id", activity.getId());
        map.put("title", activity.getTitle());
        map.put("status", activity.getStatus());
        map.put("inviteRewardBean", activity.getInviteRewardBean());
        map.put("inviteMaxAmount", activity.getInviteMaxAmount());
        map.put("startDate", activity.getStartDate());
        map.put("endDate", activity.getEndDate());
        return R.ok(map);
    }
}
