package com.ruoyi.web.controller.lottery;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.raffle.domain.LotteryItem;
import com.ruoyi.raffle.service.ILotteryItemService;
import com.ruoyi.third.party.service.IOssFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 抽奖奖项表 控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/lottery/item")
@Api("奖品仓库管理")
public class LotteryItemController extends BaseController {

    @Resource
    private ILotteryItemService lotteryItemService;

    @Resource
    private IOssFileService ossFileService;


    /**
     * 查询抽奖奖项列表
     */
    //@PreAuthorize("@ss.hasPermi('lottery:item:list')")
    @GetMapping("/list")
    @ApiOperation("查询奖品列表")
    public TableDataInfo list(LotteryItem lotteryItem) {
        startPage();
        LambdaQueryWrapper<LotteryItem> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(lotteryItem.getName())) {
            queryWrapper.like(LotteryItem::getName, lotteryItem.getName());
        }
        if (lotteryItem.getType() != null) {
            queryWrapper.eq(LotteryItem::getType, lotteryItem.getType());
        }
        queryWrapper.orderByDesc(LotteryItem::getId);
        List<LotteryItem> list = lotteryItemService.list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)){
            list.forEach(e -> {
                e.setCoverUrl(ossFileService.getOriUrl(e.getCover()));
            });
        }
        return getDataTable(list);
    }

    /**
     * 获取抽奖奖项详细信息
     */
    /*//@PreAuthorize("@ss.hasPermi('lottery:item:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(lotteryItemService.getById(id));
    }*/

    /**
     * 新增抽奖奖项
     */
    //@PreAuthorize("@ss.hasPermi('lottery:item:add')")
    @Log(title = "抽奖奖项", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增奖品")
    public AjaxResult add(@RequestBody LotteryItem lotteryItem) {
        if (Objects.nonNull(lotteryItem)) {
            if (Objects.equals(3, lotteryItem.getType())) {
                lotteryItem.setPrizeMin(new BigDecimal("1.00"));
                lotteryItem.setPrizeMax(new BigDecimal("1.00"));
            }
            lotteryItem.setCreateUserId(getLoginUser().getUserId());
            lotteryItem.setCreateUserName(getLoginUser().getUsername());
            lotteryItem.setCreateDate(DateUtils.getNowDate());
        }
        return toAjax(lotteryItemService.save(lotteryItem));
    }

    /**
     * 修改抽奖奖项
     */
    //@PreAuthorize("@ss.hasPermi('lottery:item:edit')")
    @Log(title = "抽奖奖项", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改奖品")
    public AjaxResult edit(@RequestBody LotteryItem lotteryItem) {
        if (Objects.nonNull(lotteryItem)) {

            lotteryItem.setUpdateUserId(getLoginUser().getUserId());
            lotteryItem.setUpdateUserName(getLoginUser().getUsername());
            lotteryItem.setUpdateDate(DateUtils.getNowDate());
        }
        return toAjax(lotteryItemService.updateById(lotteryItem));
    }

    /**
     * 删除抽奖奖项
     */
    /*//@PreAuthorize("@ss.hasPermi('lottery:item:remove')")
    @Log(title = "抽奖奖项", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(lotteryItemService.removeByIds(Arrays.asList(ids)));
    }*/

    /**
     * 查询抽奖奖项列表
     */
    @GetMapping("/dict")
    @ApiOperation("查询奖品字典列表")
    public R<List<Map<String, Object>>> dictlist(LotteryItem lotteryItem) {
        LambdaQueryWrapper<LotteryItem> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(lotteryItem.getName())) {
            queryWrapper.like(LotteryItem::getName, lotteryItem.getName());
        }
        if (lotteryItem.getType() != null) {
            queryWrapper.eq(LotteryItem::getType, lotteryItem.getType());
        }
        queryWrapper.orderByDesc(LotteryItem::getId);
        List<LotteryItem> list = lotteryItemService.list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)){
            return R.ok(list.stream().map(e -> {
                Map<String, Object> map = new HashMap<>();
                map.put("id", e.getId());
                map.put("name", e.getName());
                map.put("coverUrl", ossFileService.getOriUrl(e.getCover()));
                return map;
            }).collect(Collectors.toList()));
        }else{
            return  R.ok(Collections.emptyList());
        }
    }
}