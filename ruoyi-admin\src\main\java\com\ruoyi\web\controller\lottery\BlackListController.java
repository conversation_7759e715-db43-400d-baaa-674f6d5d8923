package com.ruoyi.web.controller.lottery;

import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.raffle.domain.BlackList;
import com.ruoyi.raffle.service.IBlackListService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 抽奖奖项表 控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/black/list")
@Api("黑名单管理")
public class BlackListController extends BaseController {

    @Resource
    private IBlackListService blackListService;


    /**
     * 查询抽奖奖项列表
     */
   // //@PreAuthorize("@ss.hasPermi('lottery:item:list')")
    @GetMapping("/list")
    @ApiOperation("查询黑名单列表")
    public TableDataInfo list(BlackList blackList) {
        startPage();
        LambdaQueryWrapper<BlackList> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(blackList.getReason())) {
            queryWrapper.like(BlackList::getReason, blackList.getReason());
        }
        if (StringUtils.isNotBlank(blackList.getOpenId())) {
            queryWrapper.eq(BlackList::getOpenId, blackList.getOpenId());
        }
        queryWrapper.orderByDesc(BlackList::getId);
        List<BlackList> list = blackListService.list(queryWrapper);
        return getDataTable(list);
    }


    @Log(title = "黑名单", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    @ApiOperation("导入")
    public AjaxResult importData(MultipartFile file) throws Exception
    {
        ExcelUtil<BlackList> util = new ExcelUtil<BlackList>(BlackList.class);
        List<BlackList> list = util.importExcel(file.getInputStream());
        list.forEach(e -> {
            this.blackListService.addToBlackList(e.getOpenId(), e.getReason());
        });

        return success();
    }
    @PostMapping("/code/importTemplate")
    @ApiOperation("下载黑名单导入模板")
    public void importTemplate(HttpServletResponse response)
    {

        response.setHeader("Content-disposition", "attachment;filename=blacklist.xlsx");
        ExcelUtil<BlackList> util = new ExcelUtil<BlackList>(BlackList.class);
        util.importTemplateExcel(response, "Sheet1");
    }

    /**
     * 删除抽奖奖项
     */
    ////@PreAuthorize("@ss.hasPermi('lottery:item:remove')")
    @Log(title = "黑名单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除黑名单")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(blackListService.softDeleteByIds(Arrays.asList(ids)));
    }

}