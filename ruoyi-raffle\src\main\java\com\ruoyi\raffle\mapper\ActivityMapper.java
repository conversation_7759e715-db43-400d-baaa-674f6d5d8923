package com.ruoyi.raffle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.raffle.domain.Activity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/6 4:56 下午
 */

public interface ActivityMapper extends BaseMapper<Activity> {
    List<Activity> queryActiveList(@Param("tenantId") String tenantId);
    List<Activity> queryPublicActivityList(@Param("tenantId") String tenantId);
}
