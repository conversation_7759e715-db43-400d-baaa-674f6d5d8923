package com.ruoyi.raffle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.raffle.domain.LotteryPrize;
import com.ruoyi.raffle.domain.dto.LotteryPrizeDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 抽奖奖品表 Mapper 接口
 * 
 * <AUTHOR>
 */
public interface LotteryPrizeMapper extends BaseMapper<LotteryPrize> {


    List<LotteryPrizeDTO> queryPrizeList(@Param("activityId") Long activityId,@Param("activityDate") String activityDate,@Param("isLimitCurrentTime") Boolean isLimitCurrentTime);

    List<LotteryPrizeDTO> queryPrizePools(@Param("activityId") Long activityId,@Param("activityDate") String activityDate);

}