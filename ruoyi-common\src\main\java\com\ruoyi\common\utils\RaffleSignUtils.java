package com.ruoyi.common.utils;

import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Comparator;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

public class RaffleSignUtils {
    /**
     * 短信验证码秘钥
     */
    public static final String SMS_SECRET = "N9xu_&T4$qHjo#kV";


    /**
     * 签名算法
     *
     * @param map    参数列表
     * @param secret 秘钥
     * @param t      时间戳
     * @return 签名
     */
    public static String sign(Map<String, Object> map, String secret, long t) {
        //  Map按key进行排序
        Map<String, Object> sortMap = sortMapByKey(map);
        //  构建查询参数
        String httpQuery = buildHttpQuery(sortMap);
        //  加密前字符串
        String str = httpQuery + "&appkey=" + secret + "&timestamp=" + t;
        //  Md5加密
        return Md5Utils.encrypt(str);
    }


    /**
     * 使用 Map按key进行排序
     *
     * @param map 参数列表
     * @return 排序后map
     */
    private static Map<String, Object> sortMapByKey(Map<String, Object> map) {
        if (CollectionUtils.isEmpty(map)) {
            return Collections.emptyMap();
        }
        Map<String, Object> sortMap = new TreeMap<>(Comparator.naturalOrder());
        sortMap.putAll(map);
        return sortMap;
    }

    private static String buildHttpQuery(Map<String, Object> params) {
        return params.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()).collect(Collectors.joining("&"));
    }


}
