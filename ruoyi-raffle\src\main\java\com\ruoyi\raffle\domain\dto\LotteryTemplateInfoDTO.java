package com.ruoyi.raffle.domain.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/7 4:56 下午
 */

@Data
public class LotteryTemplateInfoDTO {
    /**
     * 模板id
     */
    private Long id;

    /**
     * 奖项id
     */
    private Long itemId;

    /**
     * 奖品类型 0-优先 1-保底
     */
    private Integer prizeType;

    /**
     * 用户中奖策略：0-不限制 1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中
     */
    private Integer userStrategy;
    /**
     * 设备中奖策略：0-不限制 1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中
     */
    private Integer deviceStrategy;

    /**
     * 互斥的奖项id集合
     */
    private String mutuallyItemIds;

    /**
     * 顺序
     */
    private Integer sort;


    /**
     * 类型: 1-现金 2-今豆 3-物品
     */
    private Integer type;

    /**
     * 奖项名称
     */
    private String name;

    /**
     * 奖项图片
     */
    private String cover;

}
