package com.ruoyi.raffle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.raffle.domain.BlackList;

import java.util.List;

/**
 * 黑名单业务逻辑接口
 * 提供黑名单相关的业务操作方法
 * 
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface IBlackListService extends IService<BlackList> {
    /**
     * 添加用户到黑名单
     *
     * @param openId 微信用户唯一标识
     * @param reason 拉黑理由
     * @return 是否添加成功
     */
    boolean addToBlackList(String openId, String reason);
    /**
     * 检查用户是否在黑名单中
     *
     * @param openId 微信用户唯一标识
     * @return true-在黑名单中，false-不在黑名单中
     */
    boolean isInBlackList(String openId);


    /**
     * 根据openId查询黑名单记录
     *
     * @param openId 微信用户唯一标识
     * @return 黑名单记录，如果不存在则返回null
     */
    BlackList getByOpenId(String openId);

    /**
     * 软删除黑名单记录
     *
     * @param ids 黑名单记录id
     * @return 是否删除成功
     */
    boolean softDeleteByIds(List<Long> ids);

}
