package com.ruoyi.raffle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.raffle.domain.LotteryPrizeTemplate;
import com.ruoyi.raffle.domain.dto.LotteryTemplateInfoDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 抽奖奖品模板表数据访问层
 * 提供奖品模板相关的数据库操作方法
 * 
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface LotteryPrizeTemplateMapper extends BaseMapper<LotteryPrizeTemplate> {

    List<LotteryTemplateInfoDTO> queryTemplateInfoList(@Param("activityId") Long activityId);
}
