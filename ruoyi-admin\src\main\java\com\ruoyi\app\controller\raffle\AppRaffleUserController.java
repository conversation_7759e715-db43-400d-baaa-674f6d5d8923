package com.ruoyi.app.controller.raffle;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.exception.user.BlackListException;
import com.ruoyi.common.exception.user.UserPasswordNotMatchException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.MessageUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.framework.manager.AsyncManager;
import com.ruoyi.framework.manager.factory.AsyncFactory;
import com.ruoyi.framework.security.context.AuthenticationContextHolder;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.raffle.domain.RaffleUser;
import com.ruoyi.raffle.domain.dto.request.RaffleUserLoginRequest;
import com.ruoyi.raffle.domain.dto.response.RaffleUserLoginResponse;
import com.ruoyi.raffle.service.IRaffleUserService;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysTenantService;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.third.party.domain.dto.response.jsp.JspUserResponse;
import com.ruoyi.third.party.service.IJspService;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.Objects;

@RestController
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
@RequestMapping("/app/raffle/user")
public class AppRaffleUserController extends BaseController {

    private final ISysTenantService sysTenantService;

    private final IRaffleUserService raffleUserService;

    private final ISysUserService userService;

    private final TokenService tokenService;

    private final ISysConfigService configService;

    private final AuthenticationManager authenticationManager;

    private final RestTemplate restTemplate;

    private final IJspService jspService;


    @ApiOperation("抽奖用户登录")
    @PostMapping("/login")
    @Transactional(rollbackFor = Exception.class)
    public R<RaffleUserLoginResponse> login(@RequestBody RaffleUserLoginRequest request) {

        String mobile = "";
        String nickname = request.getNickname();
        String avatar = request.getAvatar();
        // 检查租户信息
        String tenantId = request.getAppId();
        String openId = request.getUserId();
        SysTenant tenant = sysTenantService.getById(tenantId);
        if (Objects.isNull(tenant)) {
            return R.fail("租户不合法");
        }

        // 检查token是否有效
        if (Objects.equals(tenant.getPlatform(), 1)) {

            //  判断返回结果
            JspUserResponse jspUserResponse = jspService.getUserInfo(request.getToken());
            if (Objects.isNull(jspUserResponse) || !Objects.equals(jspUserResponse.getCode(), 0)) {
                throw new ServiceException(HttpStatus.UNAUTHORIZED,"获取用户信息失败");
            }
            JspUserResponse.ResultDTO result = jspUserResponse.getResult();
            //  获取手机号
            mobile = result.getPhone();
            nickname = result.getNickname();
            avatar = result.getAvatarImageUrl();
            openId = result.getUserId()+"";

        } else {
            // todo 赣云平台检查

        }

        String username = tenantId + "|" + openId;
        // IP黑名单校验
        String blackStr = configService.selectConfigByKey("sys.login.blackIPList");
        if (IpUtils.isMatchedIp(blackStr, IpUtils.getIpAddr())) {
            AsyncManager.me().execute(AsyncFactory.recordLoginInfo(username, Constants.LOGIN_FAIL, MessageUtils.message("login.blocked")));
            throw new BlackListException();
        }

        //  检查用户是否存在
        RaffleUser raffleUser = raffleUserService.getByTenantIdAndOpenId(tenantId, openId);
        if (Objects.nonNull(raffleUser)) {
            //  更新用户信息
            raffleUser.setAvatar(avatar);
            raffleUser.setNickname(nickname);
            raffleUser.setMobile(mobile);
            raffleUser.setUpdateDate(new Date());
            raffleUserService.updateById(raffleUser);
        } else {
            //  创建系统用户信息
            SysUser sysUser = new SysUser();
            sysUser.setUserName(username);
            sysUser.setNickName(nickname);
            sysUser.setPassword(SecurityUtils.encryptPassword(username));
            sysUser.setTenantId(tenantId);
            userService.registerUser(sysUser);


            //  创建抽奖用户信息
            raffleUser = new RaffleUser();
            raffleUser.setUserId(sysUser.getUserId());
            raffleUser.setOpenId(openId);
            raffleUser.setAvatar(avatar);
            raffleUser.setNickname(nickname);
            raffleUser.setMobile(mobile);
            raffleUser.setTenantId(tenantId);
            raffleUser.setStatus(1);
            raffleUser.setIsDeleted(false);
            raffleUser.setIsSubscribeNewActivityNotification(false);

            raffleUserService.save(raffleUser);
        }

        // 用户验证
        Authentication authentication = null;
        try {
            UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(username, username);
            AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLoginInfo(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLoginInfo(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new ServiceException(e.getMessage());
            }
        } finally {
            AuthenticationContextHolder.clearContext();
        }
        //  记录登录信息
        AsyncManager.me().execute(AsyncFactory.recordLoginInfo(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        //  更新最近登录信息
        SysUser sysUser = new SysUser();
        sysUser.setUserId(raffleUser.getUserId());
        sysUser.setLoginIp(IpUtils.getIpAddr());
        sysUser.setLoginDate(DateUtils.getNowDate());
        userService.updateUserProfile(sysUser);
        if (Objects.equals(tenant.getPlatform(), 1)) {
            loginUser.setTicket(request.getToken());
            loginUser.setOpenId(openId);
            loginUser.setDeviceId(request.getDeviceId());
        }
        // 生成token
        String token = tokenService.createToken(loginUser);

        RaffleUserLoginResponse response = new RaffleUserLoginResponse();
        response.setIsSubNewNotification(raffleUser.getIsSubscribeNewActivityNotification());
        response.setAccessToken(token);
        response.setUserId(sysUser.getUserId());
        response.setNickname(raffleUser.getNickname());
        response.setAvatar(raffleUser.getAvatar());
        return R.ok(response);
    }


}
