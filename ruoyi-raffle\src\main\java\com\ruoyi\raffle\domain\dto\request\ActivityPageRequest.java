package com.ruoyi.raffle.domain.dto.request;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/7 3:35 下午
 */

@Data
public class ActivityPageRequest {


    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    private String tenantId;

    /**
     * 活动状态：0-活动中、1-已结束
     */
    private List<Integer> statuses;

    /**
     * 创建开始时间
     */
    private Date createDateStart;

    /**
     * 创建结束时间
     */
    private Date createDateEnd;

    /**
     * 开奖开始时间
     */
    private Date raffleOpenTimeStart;

    /**
     * 开奖结束时间
     */
    private Date raffleOpenTimeEnd;

    private Integer pageNum;
    private Integer pageSize;
    /**
     * 排序数组
     */
    private List<Sort> sorts;


    @Data
    @AllArgsConstructor
    public static class Sort {
        private String orderBy;
        private boolean isAsc;
    }
}