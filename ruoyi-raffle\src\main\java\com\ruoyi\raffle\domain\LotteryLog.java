package com.ruoyi.raffle.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 抽奖日志表实体类
 * 用于记录用户的抽奖行为和中奖结果，支持完整的抽奖审计
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Data
@TableName("lottery_log")
public class LotteryLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动id
     */
    @TableField("activity_id")
    private Long activityId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 设备ID
     */
    @TableField("device_id")
    private String deviceId;

    /**
     * IP地址
     */
    @TableField("ip_addr")
    private String ipAddr;

    /**
     * ip地点
     */
    @TableField("ip_location")
    private String ipLocation;

    /**
     * 奖项id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 奖项类型: 1-现金 2-今豆 3-物品
     */
    @TableField("item_type")
    private Integer itemType;

    /**
     * 奖项名称
     */
    @TableField("item_name")
    private String itemName;

    /**
     * 奖项值
     */
    @TableField("item_val")
    private BigDecimal itemVal;

    /**
     * 奖品ID
     */
    @TableField("prize_id")
    private Long prizeId;

    /**
     * 奖品类型 0-优先 1-保底
     */
    @TableField("prize_type")
    private Integer prizeType;

    /**
     * 发放日期
     */
    @TableField("issue_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date issueDate;

    /**
     * 绑定的姓名
     */
    @TableField("bind_name")
    private String bindName;

    /**
     * 绑定的手机号
     */
    @TableField("bind_mobile")
    private String bindMobile;

    /**
     * 券码
     */
    @TableField("coupon_code")
    private String couponCode;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 更新时间
     */
    @TableField("update_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}