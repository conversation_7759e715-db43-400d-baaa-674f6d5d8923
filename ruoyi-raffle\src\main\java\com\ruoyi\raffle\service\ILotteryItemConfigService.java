package com.ruoyi.raffle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.raffle.domain.LotteryItemConfig;
import com.ruoyi.raffle.domain.dto.LotteryItemConfigInfoDTO;
import com.ruoyi.raffle.domain.dto.LotteryTemplateInfoDTO;

import java.util.List;

/**
 * 抽奖奖品配置表业务逻辑接口
 * 提供奖品配置相关的业务操作方法
 * 
 * <AUTHOR>
 * @date 2023-12-19
 */
public interface ILotteryItemConfigService extends IService<LotteryItemConfig> {

    List<LotteryItemConfigInfoDTO> queryItemConfigInfoList(Long activityId);
}
