package com.ruoyi.third.party.domain.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AppletRouteQueryRequest {


    @JsonProperty("eventId")
    private Long eventId;
    @JsonProperty("interface")
    private InterfaceDTO interfaceX;
    @JsonProperty("version")
    private String version;
    @JsonProperty("caller")
    private String caller;
    @JsonProperty("seqId")
    private String seqId;
    @JsonProperty("timestamp")
    private Long timestamp;

    @NoArgsConstructor
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class InterfaceDTO {
        @JsonProperty("param")
        private ParamDTO param;
        @JsonProperty("name")
        private String name;

        @NoArgsConstructor
        @Data
        @JsonInclude(JsonInclude.Include.NON_NULL)
        public static class ParamDTO {
            @JsonProperty("packageId")
            private String packageId;
            @JsonProperty("path")
            private String path;
            @JsonProperty("env")
            private Integer env;
            @JsonProperty("query")
            private QueryDTO query;

            @NoArgsConstructor
            @Data
            @JsonInclude(JsonInclude.Include.NON_NULL)
            public static class QueryDTO {
                @JsonProperty("activityId")
                private Long activityId;
            }
        }
    }
}
