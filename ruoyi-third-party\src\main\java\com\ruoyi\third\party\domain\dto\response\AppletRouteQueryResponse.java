package com.ruoyi.third.party.domain.dto.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class AppletRouteQueryResponse {

    @JsonProperty("seqId")
    private String seqId;
    @JsonProperty("eventId")
    private Integer eventId;
    @JsonProperty("component")
    private String component;
    @JsonProperty("code")
    private Integer code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private DataDTO data;
    @JsonProperty("timestamp")
    private Integer timestamp;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        @JsonProperty("applet")
        private AppletDTO applet;
        @JsonProperty("url_list")
        private List<UrlListDTO> urlList;

        @NoArgsConstructor
        @Data
        public static class AppletDTO {
            @JsonProperty("icon")
            private String icon;
            @JsonProperty("name")
            private String name;
        }

        @NoArgsConstructor
        @Data
        public static class UrlListDTO {
            @JsonProperty("platform_id")
            private String platformId;
            @JsonProperty("name")
            private String name;
            @JsonProperty("url")
            private String url;
        }
    }
}
