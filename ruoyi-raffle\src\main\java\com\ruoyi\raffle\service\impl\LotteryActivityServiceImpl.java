package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.raffle.domain.*;
import com.ruoyi.raffle.domain.dto.request.LotteryActivityConfigRequest;
import com.ruoyi.raffle.mapper.LotteryActivityMapper;
import com.ruoyi.raffle.service.*;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 抽奖活动表 服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class LotteryActivityServiceImpl extends ServiceImpl<LotteryActivityMapper, LotteryActivity> implements ILotteryActivityService {

    @Resource
    private ILotteryPrizeService lotteryPrizeService;

    @Resource
    private ILotteryPrizeTimePeriodService lotteryPrizeTimePeriodService;

    @Resource
    private ILotteryPrizeTemplateService lotteryPrizeTemplateService;

    @Resource
    private ILotteryPrizeTemplateTimePeriodService lotteryPrizeTemplateTimePeriodService;

    @Resource
    private ILotteryItemConfigService lotteryItemConfigService;

    @Resource
    private ILotteryItemService lotteryItemService;

    @Resource
    private ILotteryItemCodeService lotteryItemCodeService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveActivityConfig(LotteryActivityConfigRequest request) {
        try {
            // 参数校验
            if (request == null || request.getActivityInfo() == null) {
                throw new ServiceException("活动配置参数错误");
            }

            // 2. 奖品模板配置校验
            if (!CollectionUtils.isEmpty(request.getTemplates())) {
                // 校验模板配置
                if (!validateTemplateConfigs(request.getTemplates())) {
                    throw new ServiceException("奖品模板配置校验失败");
                }

                long count = request.getTemplates().stream().filter(e -> Objects.equals(1, e.getPrizeType())).count();
                if (count == 0) {
                    throw new ServiceException("奖品模板未配置保底");
                }
            }

            // 3. 奖品日期配置校验
            if (!CollectionUtils.isEmpty(request.getPrizeDates())) {
                // 校验奖品日期配置
                if (!validatePrizeDateConfigs(request.getPrizeDates())) {
                    throw new ServiceException("奖品日期配置校验失败");
                }
            }


            // 1. 保存活动基本信息
            LotteryActivity activity = convertToActivity(request.getActivityInfo());
            if (!this.save(activity)) {
                log.error("保存活动基本信息失败");
                return false;
            }

            Long activityId = activity.getId();
            log.info("保存活动基本信息成功，活动ID: {}", activityId);

            // 2. 保存奖品模板配置
            if (!CollectionUtils.isEmpty(request.getTemplates())) {
                if (!saveTemplateConfigs(activityId, request.getTemplates())) {
                    log.error("保存奖品模板配置失败");
                    throw new ServiceException("保存奖品模板配置失败");
                }
            }

            // 3. 保存奖品日期配置
            if (!CollectionUtils.isEmpty(request.getPrizeDates())) {
                if (!savePrizeDateConfigs(activityId, request.getPrizeDates())) {
                    log.error("保存奖品配置失败");
                    throw new ServiceException("保存奖品配置失败");
                }
            }

            // 4. 处理物品类型奖品的配置
            if (!processItemConfigs(activityId, request.getPrizeDates())) {
                log.error("处理物品类型奖品配置失败");
                throw new ServiceException("处理物品类型奖品配置失败");
            }

            log.info("保存活动配置成功，活动ID: {}", activityId);
            return true;

        } catch (Exception e) {
            log.error("保存活动配置异常", e);
            throw new ServiceException("保存活动配置失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateActivityConfig(LotteryActivityConfigRequest request) {
        try {
            // 参数校验
            if (request == null || request.getActivityInfo() == null || request.getActivityInfo().getId() == null) {
                throw new ServiceException("活动配置参数错误");
            }
            // 2. 奖品模板配置校验
            if (!CollectionUtils.isEmpty(request.getTemplates())) {
                // 校验模板配置
                if (!validateTemplateConfigs(request.getTemplates())) {
                    throw new ServiceException("奖品模板配置校验失败");
                }

                long count = request.getTemplates().stream().filter(e -> Objects.equals(1, e.getPrizeType())).count();
                if (count == 0) {
                    throw new ServiceException("奖品模板未配置保底");
                }
            }

            // 3. 奖品日期配置校验
            if (!CollectionUtils.isEmpty(request.getPrizeDates())) {
                // 校验奖品日期配置
                if (!validatePrizeDateConfigs(request.getPrizeDates())) {
                    throw new ServiceException("奖品日期配置校验失败");
                }
            }


            Long activityId = request.getActivityInfo().getId();
            log.info("开始更新活动配置，活动ID: {}", activityId);
            LotteryActivity lotteryActivity = this.getById(activityId);
            if (Objects.isNull(lotteryActivity) || Objects.equals(1,lotteryActivity.getDelFlag())){
                throw new ServiceException("活动不存在或已删除");
            }

            // 1. 更新活动基本信息
            LotteryActivity activity = convertToActivity(request.getActivityInfo());
            if (!this.updateById(activity)) {
                log.error("更新活动基本信息失败");
                return false;
            }

            if (!updateTemplateConfigs(activityId, request.getTemplates())) {
                log.error("更新奖品模板配置失败");
                throw new ServiceException("更新奖品模板配置失败");
            }

            if (!updatePrizeDateConfigsWithDateFilter(activityId,lotteryActivity.getStatus(), request.getPrizeDates())) {
                log.error("更新奖品日期配置失败");
                throw new ServiceException("更新奖品日期配置失败");
            }

            // 4. 处理物品类型奖品的配置
            if (!processItemConfigs(activityId, request.getPrizeDates())) {
                log.error("处理物品类型奖品配置失败");
                throw new ServiceException("处理物品类型奖品配置失败");
            }

            log.info("更新活动配置成功，活动ID: {}", activityId);
            return true;

        } catch (Exception e) {
            log.error("更新活动配置异常", e);
            throw new ServiceException("更新活动配置失败: " + e.getMessage());
        }
    }

    @Override
    public List<String> checkActivityConfig(Long activityId) {
        try {
            if (activityId == null) {
                return Collections.emptyList();
            }

            List<String> errorMessages = new ArrayList<>();

            // 1. 校验券码数量与库存一致性
            List<String> codeStockErrors = checkCodeStockConsistencyAll(activityId);
            if (!CollectionUtils.isEmpty(codeStockErrors)) {
                errorMessages.addAll(codeStockErrors);
            }

            // 2. 校验LotteryItemConfig的bindMobileFlag字段
            List<String> bindMobileFlagErrors = checkBindMobileFlagConfigAll(activityId);
            if (!CollectionUtils.isEmpty(bindMobileFlagErrors)) {
                errorMessages.addAll(bindMobileFlagErrors);
            }

            // 返回所有错误信息，如果没有错误则返回空列表
            return errorMessages;

        } catch (Exception e) {
            log.error("校验活动配置异常，活动ID: {}", activityId, e);
        }
        return Collections.emptyList();
    }

    /**
     * 转换活动信息
     */
    private LotteryActivity convertToActivity(LotteryActivityConfigRequest.ActivityInfo activityInfo) {
        LotteryActivity activity = new LotteryActivity();

        // 设置基本信息
        if (activityInfo.getId() != null) {
            activity.setId(activityInfo.getId());
        }
        activity.setTitle(activityInfo.getTitle());
        activity.setIntroduction(activityInfo.getIntroduction());
        activity.setStatus(activityInfo.getStatus());
        activity.setType(activityInfo.getType());

        // 转换日期格式
        if (StringUtils.hasText(activityInfo.getStartDate())) {
            activity.setStartDate(LocalDate.parse(activityInfo.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).atStartOfDay());
        }
        if (StringUtils.hasText(activityInfo.getEndDate())) {
            activity.setEndDate(LocalDate.parse(activityInfo.getEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")).atTime(23, 59, 59));
        }

        // 设置其他配置
        activity.setLotteryAmount(activityInfo.getLotteryAmount());
        activity.setShareMaxAmount(activityInfo.getShareMaxAmount());
        activity.setDepleteBeanType(activityInfo.getDepleteBeanType());
        activity.setDepleteBean(activityInfo.getDepleteBean());
        activity.setDeviceLimit(activityInfo.getDeviceLimit());
        activity.setWatchDuration(activityInfo.getWatchDuration());
        activity.setWatchFactor(activityInfo.getWatchFactor());
        activity.setInviteFactor(activityInfo.getInviteFactor());
        activity.setInviteMaxAmount(activityInfo.getInviteMaxAmount());
        activity.setInviteRewardBean(activityInfo.getInviteRewardBean());
        activity.setBackstopBeanMin(activityInfo.getBackstopBeanMin());
        activity.setBackstopBeanMax(activityInfo.getBackstopBeanMax());
        activity.setLimitIpAddr(activityInfo.getLimitIpAddr());

        // 设置创建/更新信息
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        java.util.Date now = new java.util.Date();

        if (activityInfo.getId() == null) {
            // 新增
            activity.setCreateUserId(currentUser.getUserId());
            activity.setCreateUserName(currentUser.getUserName());
            activity.setCreateDate(now);
        } else {
            // 更新
            activity.setUpdateUserId(currentUser.getUserId());
            activity.setUpdateUserName(currentUser.getUserName());
            activity.setUpdateDate(now);
        }

        return activity;
    }

    /**
     * 保存奖品模板配置（优化版）
     */
    private boolean saveTemplateConfigs(Long activityId, List<LotteryActivityConfigRequest.TemplateInfo> templates) {
        if (CollectionUtils.isEmpty(templates)) {
            return true;
        }

        try {
            // 使用Stream API优化数据处理
            List<TemplateWithTimePeriods> templateDataList = templates.stream().map(templateInfo -> new TemplateWithTimePeriods(convertToTemplate(activityId, templateInfo), templateInfo.getTimePeriods())).collect(Collectors.toList());

            // 提取模板列表
            List<LotteryPrizeTemplate> templateList = templateDataList.stream().map(TemplateWithTimePeriods::getTemplate).collect(Collectors.toList());

            // 批量保存模板
            if (!lotteryPrizeTemplateService.saveBatch(templateList)) {
                log.error("批量保存模板失败");
                return false;
            }

            // 批量生成时间段配置
            List<LotteryPrizeTemplateTimePeriod> timePeriodList = templateDataList.stream()
                    // .filter(data -> !CollectionUtils.isEmpty(data.getTimePeriods()))
                    .flatMap(data -> convertToTemplateTimePeriods(data.getTemplate().getId(), data.getTimePeriods()).stream()).collect(Collectors.toList());

            // 批量保存时间段配置
            if (!CollectionUtils.isEmpty(timePeriodList)) {
                return lotteryPrizeTemplateTimePeriodService.saveBatch(timePeriodList);
            }

            return true;

        } catch (Exception e) {
            log.error("保存奖品模板配置异常", e);
            return false;
        }
    }

    /**
     * 内部类：模板和时间段配置的组合
     */
    @Getter
    private static class TemplateWithTimePeriods {
        private final LotteryPrizeTemplate template;
        private final List<LotteryActivityConfigRequest.TimePeriodInfo> timePeriods;

        public TemplateWithTimePeriods(LotteryPrizeTemplate template, List<LotteryActivityConfigRequest.TimePeriodInfo> timePeriods) {
            this.template = template;
            this.timePeriods = timePeriods;
        }

    }

    /**
     * 转换为奖品模板
     */
    private LotteryPrizeTemplate convertToTemplate(Long activityId, LotteryActivityConfigRequest.TemplateInfo templateInfo) {
        LotteryPrizeTemplate template = new LotteryPrizeTemplate();

        if (templateInfo.getId() != null) {
            template.setId(templateInfo.getId());
        }
        template.setActivityId(activityId);
        template.setItemId(templateInfo.getItemId());
        template.setPrizeType(templateInfo.getPrizeType());
        template.setUserStrategy(templateInfo.getUserStrategy());
        template.setDeviceStrategy(templateInfo.getDeviceStrategy());
        template.setSort(templateInfo.getSort());

        // 处理互斥奖项ID
        if (!CollectionUtils.isEmpty(templateInfo.getMutuallyItemIds())) {
            String mutuallyItemIds = templateInfo.getMutuallyItemIds().stream().map(String::valueOf).collect(Collectors.joining(","));
            template.setMutuallyItemIds(mutuallyItemIds);
        }

        // 设置创建信息
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        java.util.Date now = new java.util.Date();
        template.setCreateUserId(currentUser.getUserId());
        template.setCreateUserName(currentUser.getUserName());
        template.setCreateDate(now);

        return template;
    }

    /**
     * 转换为模板时间段配置
     */
    private List<LotteryPrizeTemplateTimePeriod> convertToTemplateTimePeriods(Long templateId, List<LotteryActivityConfigRequest.TimePeriodInfo> timePeriods) {
        List<LotteryPrizeTemplateTimePeriod> result = new ArrayList<>();

        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        java.util.Date now = new java.util.Date();

        if (!CollectionUtils.isEmpty(timePeriods)) {
            for (LotteryActivityConfigRequest.TimePeriodInfo timePeriodInfo : timePeriods) {
                LotteryPrizeTemplateTimePeriod timePeriod = new LotteryPrizeTemplateTimePeriod();
                timePeriod.setTemplateId(templateId);
                timePeriod.setStartTime(LocalTime.parse(timePeriodInfo.getStartTime()));
                timePeriod.setEndTime(LocalTime.parse(timePeriodInfo.getEndTime()));
                timePeriod.setAmount(timePeriodInfo.getAmount());
                timePeriod.setCreateUserId(currentUser.getUserId());
                timePeriod.setCreateUserName(currentUser.getUserName());
                timePeriod.setCreateDate(now);

                result.add(timePeriod);
            }
        } else {
            LotteryPrizeTemplateTimePeriod timePeriod = new LotteryPrizeTemplateTimePeriod();
            timePeriod.setTemplateId(templateId);
            timePeriod.setStartTime(LocalTime.parse("00:00:00"));
            timePeriod.setEndTime(LocalTime.parse("23:59:59"));
            timePeriod.setAmount(0);
            timePeriod.setCreateUserId(currentUser.getUserId());
            timePeriod.setCreateUserName(currentUser.getUserName());
            timePeriod.setCreateDate(now);
            result.add(timePeriod);
        }

        return result;
    }

    /**
     * 转换为奖品
     */
    private LotteryPrize convertToPrize(Long activityId, String prizeDate, LotteryActivityConfigRequest.PrizeInfo prizeInfo) {
        LotteryPrize prize = new LotteryPrize();

        prize.setActivityId(activityId);
        prize.setActivityDate(LocalDate.parse(prizeDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        prize.setItemId(prizeInfo.getItemId());
        prize.setPrizeType(prizeInfo.getPrizeType());
        prize.setUserStrategy(prizeInfo.getUserStrategy());
        prize.setDeviceStrategy(prizeInfo.getDeviceStrategy());
        prize.setSort(prizeInfo.getSort());
        // prize.setIssueAmount(0); // 初始已发放数量为0

        // 处理互斥奖项ID
        if (!CollectionUtils.isEmpty(prizeInfo.getMutuallyItemIds())) {
            String mutuallyItemIds = prizeInfo.getMutuallyItemIds().stream().map(String::valueOf).collect(Collectors.joining(","));
            prize.setMutuallyItemIds(mutuallyItemIds);
        }

        // 计算总数量（从时间段配置中汇总）
        /*
         * if (!CollectionUtils.isEmpty(prizeInfo.getTimePeriods())) { int totalAmount = prizeInfo.getTimePeriods().stream() .mapToInt(LotteryActivityConfigRequest.TimePeriodInfo::getAmount) .sum(); prize.setAmount(totalAmount); }
         */

        // 设置创建信息
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        java.util.Date now = new java.util.Date();
        prize.setCreateUserId(currentUser.getUserId());
        prize.setCreateUserName(currentUser.getUserName());
        prize.setCreateDate(now);

        return prize;
    }

    /**
     * 转换为奖品时间段配置
     */
    private List<LotteryPrizeTimePeriod> convertToPrizeTimePeriods(Long prizeId, List<LotteryActivityConfigRequest.TimePeriodInfo> timePeriods) {
        List<LotteryPrizeTimePeriod> result = new ArrayList<>();

        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        java.util.Date now = new java.util.Date();

        if (!CollectionUtils.isEmpty(timePeriods)) {
            for (LotteryActivityConfigRequest.TimePeriodInfo timePeriodInfo : timePeriods) {
                LotteryPrizeTimePeriod timePeriod = new LotteryPrizeTimePeriod();
                timePeriod.setPrizeId(prizeId);
                timePeriod.setStartTime(LocalTime.parse(timePeriodInfo.getStartTime()));
                timePeriod.setEndTime(LocalTime.parse(timePeriodInfo.getEndTime()));
                timePeriod.setAmount(timePeriodInfo.getAmount());
                timePeriod.setIssueAmount(0); // 初始已发放数量为0
                timePeriod.setCreateUserId(currentUser.getUserId());
                timePeriod.setCreateUserName(currentUser.getUserName());
                timePeriod.setCreateDate(now);

                result.add(timePeriod);
            }
        } else {
            LotteryPrizeTimePeriod timePeriod = new LotteryPrizeTimePeriod();
            timePeriod.setPrizeId(prizeId);
            timePeriod.setStartTime(LocalTime.parse("00:00:00"));
            timePeriod.setEndTime(LocalTime.parse("23:59:59"));
            timePeriod.setAmount(0);
            timePeriod.setIssueAmount(0); // 初始已发放数量为0
            timePeriod.setCreateUserId(currentUser.getUserId());
            timePeriod.setCreateUserName(currentUser.getUserName());
            timePeriod.setCreateDate(now);

            result.add(timePeriod);
        }

        return result;
    }

    /**
     * 更新奖品模板配置（优化版）
     */
    private boolean updateTemplateConfigs(Long activityId, List<LotteryActivityConfigRequest.TemplateInfo> templates) {
        try {
            // 优化：使用批量删除，减少数据库交互次数
            if (!batchDeleteTemplateConfigs(activityId)) {
                log.error("删除原有模板配置失败");
                return false;
            }

            // 重新保存模板配置
            return saveTemplateConfigs(activityId, templates);

        } catch (Exception e) {
            log.error("更新奖品模板配置异常", e);
            return false;
        }
    }

    /**
     * 批量删除模板配置
     */
    private boolean batchDeleteTemplateConfigs(Long activityId) {
        try {
            // 一次查询获取所有需要删除的模板ID
            List<Long> templateIds = lotteryPrizeTemplateService.list(new LambdaQueryWrapper<LotteryPrizeTemplate>().eq(LotteryPrizeTemplate::getActivityId, activityId).select(LotteryPrizeTemplate::getId)).stream().map(LotteryPrizeTemplate::getId).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(templateIds)) {
                return true; // 没有数据需要删除
            }

            // 批量删除模板时间段配置
            boolean timePeriodDeleted = lotteryPrizeTemplateTimePeriodService.remove(new LambdaQueryWrapper<LotteryPrizeTemplateTimePeriod>().in(LotteryPrizeTemplateTimePeriod::getTemplateId, templateIds));

            // 批量删除模板
            boolean templateDeleted = lotteryPrizeTemplateService.remove(new LambdaQueryWrapper<LotteryPrizeTemplate>().eq(LotteryPrizeTemplate::getActivityId, activityId));

            log.info("删除活动{}的模板配置完成，模板数量: {}, 时间段删除: {}, 模板删除: {}", activityId, templateIds.size(), timePeriodDeleted, templateDeleted);

            return true;

        } catch (Exception e) {
            log.error("批量删除模板配置异常", e);
            return false;
        }
    }

    /**
     * 保存奖品日期配置（优化版）
     */
    private boolean savePrizeDateConfigs(Long activityId, List<LotteryActivityConfigRequest.PrizeDateInfo> prizeDates) {
        if (CollectionUtils.isEmpty(prizeDates)) {
            return true;
        }

        try {
            // 使用Stream API优化数据处理
            List<PrizeWithTimePeriods> prizeDataList = prizeDates.stream().filter(prizeDateInfo -> !CollectionUtils.isEmpty(prizeDateInfo.getPrizes())).flatMap(prizeDateInfo -> prizeDateInfo.getPrizes().stream().map(prizeInfo -> new PrizeWithTimePeriods(convertToPrize(activityId, prizeDateInfo.getPrizeDate(), prizeInfo), prizeInfo.getTimePeriods()))).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(prizeDataList)) {
                return true;
            }

            // 提取奖品列表
            List<LotteryPrize> prizeList = prizeDataList.stream().map(PrizeWithTimePeriods::getPrize).collect(Collectors.toList());

            // 批量保存奖品
            if (!lotteryPrizeService.saveBatch(prizeList)) {
                log.error("批量保存奖品失败");
                return false;
            }

            // 批量生成时间段配置
            List<LotteryPrizeTimePeriod> timePeriodList = prizeDataList.stream()
                    // .filter(data -> !CollectionUtils.isEmpty(data.getTimePeriods()))
                    .flatMap(data -> convertToPrizeTimePeriods(data.getPrize().getId(), data.getTimePeriods()).stream()).collect(Collectors.toList());

            // 批量保存时间段配置
            if (!CollectionUtils.isEmpty(timePeriodList)) {
                return lotteryPrizeTimePeriodService.saveBatch(timePeriodList);
            }

            return true;

        } catch (Exception e) {
            log.error("保存奖品日期配置异常", e);
            return false;
        }
    }

    /**
     * 内部类：奖品和时间段配置的组合
     */
    @Getter
    private static class PrizeWithTimePeriods {
        private final LotteryPrize prize;
        private final List<LotteryActivityConfigRequest.TimePeriodInfo> timePeriods;

        public PrizeWithTimePeriods(LotteryPrize prize, List<LotteryActivityConfigRequest.TimePeriodInfo> timePeriods) {
            this.prize = prize;
            this.timePeriods = timePeriods;
        }

    }

    /**
     * 更新奖品日期配置（带日期过滤）
     */
    private boolean updatePrizeDateConfigsWithDateFilter(Long activityId,Integer status, List<LotteryActivityConfigRequest.PrizeDateInfo> prizeDates) {
        try {
            LocalDate filterPrizeDate = Objects.equals(1,status) ? LocalDate.now() : LocalDate.now().minusDays(1);

            // 1. 过滤掉今天及今天之前的配置数据
            List<LotteryActivityConfigRequest.PrizeDateInfo> futurePrizeDates = filterFuturePrizeDates(prizeDates, filterPrizeDate);

            // 2. 删除今天之后的原有奖品配置（保留今天及之前的数据）
            if (!batchDeleteFuturePrizeConfigs(activityId, filterPrizeDate)) {
                log.error("删除今天之后的奖品配置失败");
                return false;
            }

            // 3. 保存今天之后的新配置
            if (!CollectionUtils.isEmpty(futurePrizeDates)) {
                if (!savePrizeDateConfigs(activityId, futurePrizeDates)) {
                    log.error("保存今天之后的奖品配置失败");
                    return false;
                }
            }

            log.info("更新奖品日期配置成功，活动ID: {}, 过滤前配置数量: {}, 过滤后配置数量: {}", activityId, prizeDates != null ? prizeDates.size() : 0, futurePrizeDates.size());

            return true;

        } catch (Exception e) {
            log.error("更新奖品日期配置异常", e);
            return false;
        }
    }

    /**
     * 校验模板配置
     */
    private boolean validateTemplateConfigs(List<LotteryActivityConfigRequest.TemplateInfo> templates) {
        if (CollectionUtils.isEmpty(templates)) {
            return true;
        }

        for (LotteryActivityConfigRequest.TemplateInfo template : templates) {
            // 校验优先奖品（prizeType==0）的时间段配置
            if (template.getPrizeType() != null && template.getPrizeType() == 0) {
                if (!validateTimePeriods(template.getTimePeriods(), "模板ID:" + template.getId())) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 校验奖品日期配置
     */
    private boolean validatePrizeDateConfigs(List<LotteryActivityConfigRequest.PrizeDateInfo> prizeDates) {
        if (CollectionUtils.isEmpty(prizeDates)) {
            return true;
        }

        for (LotteryActivityConfigRequest.PrizeDateInfo prizeDateInfo : prizeDates) {
            if (!CollectionUtils.isEmpty(prizeDateInfo.getPrizes())) {
                for (LotteryActivityConfigRequest.PrizeInfo prize : prizeDateInfo.getPrizes()) {
                    // 校验优先奖品（prizeType==0）的时间段配置
                    if (prize.getPrizeType() != null && prize.getPrizeType() == 0) {
                        String context = "日期:" + prizeDateInfo.getPrizeDate() + ",奖品ID:" + prize.getItemId();
                        if (!validateTimePeriods(prize.getTimePeriods(), context)) {
                            return false;
                        }
                    }
                }
            }
        }

        return true;
    }

    /**
     * 校验时间段配置
     *
     * @param timePeriods 时间段列表
     * @param context 上下文信息，用于错误日志
     * @return 校验是否通过
     */
    private boolean validateTimePeriods(List<LotteryActivityConfigRequest.TimePeriodInfo> timePeriods, String context) {
        // 1. 校验时间段不能为空
        if (CollectionUtils.isEmpty(timePeriods)) {
            log.error("优先奖品的时间段配置不能为空，{}", context);
            return false;
        }

        // 2. 校验每个时间段的有效性
        for (LotteryActivityConfigRequest.TimePeriodInfo timePeriod : timePeriods) {
            if (!StringUtils.hasText(timePeriod.getStartTime()) || !StringUtils.hasText(timePeriod.getEndTime())) {
                log.error("时间段的开始时间和结束时间不能为空，{}", context);
                return false;
            }

            try {
                LocalTime startTime = LocalTime.parse(timePeriod.getStartTime());
                LocalTime endTime = LocalTime.parse(timePeriod.getEndTime());

                // 校验开始时间不能大于等于结束时间
                if (!startTime.isBefore(endTime)) {
                    log.error("时间段的开始时间必须小于结束时间，开始时间:{}, 结束时间:{}, {}", timePeriod.getStartTime(), timePeriod.getEndTime(), context);
                    return false;
                }

                // 校验数量必须大于0
                if (timePeriod.getAmount() == null || timePeriod.getAmount() < 0) {
                    log.error("时间段的数量必须等于0或大于0，当前数量:{}, {}", timePeriod.getAmount(), context);
                    return false;
                }

            } catch (Exception e) {
                log.error("时间段格式错误，开始时间:{}, 结束时间:{}, {}", timePeriod.getStartTime(), timePeriod.getEndTime(), context, e);
                return false;
            }
        }

        // 3. 校验时间段不能冲突
        return validateTimePeriodsNoConflict(timePeriods, context);
    }

    /**
     * 校验时间段之间不能有冲突
     */
    private boolean validateTimePeriodsNoConflict(List<LotteryActivityConfigRequest.TimePeriodInfo> timePeriods, String context) {
        if (timePeriods.size() <= 1) {
            return true; // 只有一个或没有时间段，不存在冲突
        }

        // 将时间段按开始时间排序
        List<LotteryActivityConfigRequest.TimePeriodInfo> sortedPeriods = timePeriods.stream().sorted((p1, p2) -> LocalTime.parse(p1.getStartTime()).compareTo(LocalTime.parse(p2.getStartTime()))).collect(Collectors.toList());

        // 检查相邻时间段是否有重叠
        for (int i = 0; i < sortedPeriods.size() - 1; i++) {
            LotteryActivityConfigRequest.TimePeriodInfo current = sortedPeriods.get(i);
            LotteryActivityConfigRequest.TimePeriodInfo next = sortedPeriods.get(i + 1);

            LocalTime currentEnd = LocalTime.parse(current.getEndTime());
            LocalTime nextStart = LocalTime.parse(next.getStartTime());

            // 当前时间段的结束时间不能大于下一个时间段的开始时间
            if (!currentEnd.isBefore(nextStart) && !currentEnd.equals(nextStart)) {
                log.error("时间段存在冲突，时间段1: {}-{}, 时间段2: {}-{}, {}", current.getStartTime(), current.getEndTime(), next.getStartTime(), next.getEndTime(), context);
                return false;
            }
        }

        return true;
    }

    /**
     * 过滤出今天之后的奖品日期配置
     */
    private List<LotteryActivityConfigRequest.PrizeDateInfo> filterFuturePrizeDates(List<LotteryActivityConfigRequest.PrizeDateInfo> prizeDates, LocalDate filterPrizeDate) {

        if (CollectionUtils.isEmpty(prizeDates)) {
            return Collections.emptyList();
        }

        return prizeDates.stream().filter(prizeDateInfo -> {
            try {
                LocalDate prizeDate = LocalDate.parse(prizeDateInfo.getPrizeDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                return prizeDate.isAfter(filterPrizeDate);
            } catch (Exception e) {
                log.warn("解析奖品日期失败: {}", prizeDateInfo.getPrizeDate(), e);
                return false; // 解析失败的日期不包含在结果中
            }
        }).collect(Collectors.toList());
    }

    /**
     * 批量删除今天之后的奖品配置
     */
    private boolean batchDeleteFuturePrizeConfigs(Long activityId, LocalDate filterPrizeDate) {
        try {
            // 查询今天之后的奖品ID
            List<Long> futurePrizeIds = lotteryPrizeService.list(new LambdaQueryWrapper<LotteryPrize>().eq(LotteryPrize::getActivityId, activityId).gt(LotteryPrize::getActivityDate, Date.valueOf(filterPrizeDate)).select(LotteryPrize::getId)).stream().map(LotteryPrize::getId).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(futurePrizeIds)) {
                log.info("活动{}没有今天之后的奖品配置需要删除", activityId);
                return true; // 没有数据需要删除
            }

            // 批量删除今天之后的时间段配置
            boolean timePeriodDeleted = lotteryPrizeTimePeriodService.remove(new LambdaQueryWrapper<LotteryPrizeTimePeriod>().in(LotteryPrizeTimePeriod::getPrizeId, futurePrizeIds));

            // 批量删除今天之后的奖品
            boolean prizeDeleted = lotteryPrizeService.remove(new LambdaQueryWrapper<LotteryPrize>().eq(LotteryPrize::getActivityId, activityId).gt(LotteryPrize::getActivityDate, Date.valueOf(filterPrizeDate)));

            log.info("删除活动{}今天之后的奖品配置完成，奖品数量: {}, 时间段删除: {}, 奖品删除: {}", activityId, futurePrizeIds.size(), timePeriodDeleted, prizeDeleted);

            return true;

        } catch (Exception e) {
            log.error("批量删除今天之后的奖品配置异常", e);
            return false;
        }
    }



    /**
     * 处理物品类型奖品的配置 当奖品类型是物品时，自动创建或更新LotteryItemConfig配置
     *
     * @param activityId 活动ID
     * @param prizeDates 奖品日期配置列表
     * @return 处理是否成功
     */
    private boolean processItemConfigs(Long activityId, List<LotteryActivityConfigRequest.PrizeDateInfo> prizeDates) {
        if (CollectionUtils.isEmpty(prizeDates)) {
            return true;
        }

        try {
            // 收集所有物品类型的奖项ID
            Set<Long> itemItemIds = collectItemTypeItemIds(prizeDates);

            if (CollectionUtils.isEmpty(itemItemIds)) {
                log.info("活动{}没有物品类型的奖品，跳过LotteryItemConfig处理", activityId);
                return true;
            }

            // 批量处理物品类型奖品的配置
            return batchProcessItemConfigs(activityId, itemItemIds);

        } catch (Exception e) {
            log.error("处理物品类型奖品配置异常，活动ID: {}", activityId, e);
            return false;
        }
    }

    /**
     * 收集所有物品类型的奖项ID（优化版 - 批量查询）
     */
    private Set<Long> collectItemTypeItemIds(List<LotteryActivityConfigRequest.PrizeDateInfo> prizeDates) {
        // 1. 先收集所有的奖项ID
        Set<Long> allItemIds = prizeDates.stream().filter(prizeDateInfo -> !CollectionUtils.isEmpty(prizeDateInfo.getPrizes())).flatMap(prizeDateInfo -> prizeDateInfo.getPrizes().stream()).map(LotteryActivityConfigRequest.PrizeInfo::getItemId).filter(Objects::nonNull).collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(allItemIds)) {
            return Collections.emptySet();
        }

        // 2. 批量查询奖项信息
        return batchFilterItemTypePrizes(allItemIds);
    }

    /**
     * 批量过滤物品类型的奖项
     */
    private Set<Long> batchFilterItemTypePrizes(Set<Long> itemIds) {
        try {
            // 批量查询所有奖项信息
            List<LotteryItem> items = lotteryItemService.list(new LambdaQueryWrapper<LotteryItem>().in(LotteryItem::getId, itemIds).eq(LotteryItem::getType, 3).select(LotteryItem::getId, LotteryItem::getType));

            // 过滤出物品类型的奖项ID
            return items.stream().map(LotteryItem::getId).collect(Collectors.toSet());

        } catch (Exception e) {
            log.error("批量查询奖项类型失败，itemIds: {}", itemIds, e);
            return Collections.emptySet();
        }
    }

    /**
     * 批量处理物品类型奖品的配置
     */
    private boolean batchProcessItemConfigs(Long activityId, Set<Long> itemIds) {
        try {
            // 查询已存在的配置
            List<LotteryItemConfig> existingConfigs = lotteryItemConfigService.list(new LambdaQueryWrapper<LotteryItemConfig>().eq(LotteryItemConfig::getActivityId, activityId));
            Set<Long> existingItemIds = existingConfigs.stream().map(LotteryItemConfig::getItemId).collect(Collectors.toSet());

            // 找出需要新增的配置
            Set<Long> newItemIds = itemIds.stream().filter(itemId -> !existingItemIds.contains(itemId)).collect(Collectors.toSet());

            if (CollectionUtils.isEmpty(newItemIds)) {
                log.info("活动{}的所有物品类型奖品配置已存在，无需新增", activityId);
                return true;
            }

            // 批量创建新的配置
            List<LotteryItemConfig> newConfigs = createDefaultItemConfigs(activityId, newItemIds);

            if (!CollectionUtils.isEmpty(newConfigs)) {
                boolean success = lotteryItemConfigService.saveBatch(newConfigs);
                log.info("活动{}批量创建物品类型奖品配置完成，新增数量: {}, 结果: {}", activityId, newConfigs.size(), success);
                return success;
            }

            return true;

        } catch (Exception e) {
            log.error("批量处理物品类型奖品配置异常，活动ID: {}", activityId, e);
            return false;
        }
    }

    /**
     * 创建默认的物品配置
     */
    private List<LotteryItemConfig> createDefaultItemConfigs(Long activityId, Set<Long> itemIds) {
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        java.util.Date now = new java.util.Date();

        return itemIds.stream().map(itemId -> {
            LotteryItemConfig config = new LotteryItemConfig();
            config.setActivityId(activityId);
            config.setItemId(itemId);
            // 设置创建信息
            config.setCreateUserId(currentUser.getUserId());
            config.setCreateUserName(currentUser.getUserName());
            config.setCreateDate(now);

            return config;
        }).collect(Collectors.toList());
    }

    /**
     * 校验券码数量与库存一致性（返回所有错误）
     */
    private List<String> checkCodeStockConsistencyAll(Long activityId) {


        try {
            // 1. 查询活动下所有需要发放券码的奖项
            List<LotteryItem> codeGrantItems = getCodeGrantItems(activityId);

            if (CollectionUtils.isEmpty(codeGrantItems)) {
                log.info("活动{}没有需要发放券码的奖项，跳过券码库存校验", activityId);
                return Collections.emptyList(); // 没有需要发放券码的奖项，返回空列表
            }
            List<String> errorMessages = new ArrayList<>();
            // 2. 批量校验每个奖项的券码数量与库存一致性，收集所有错误
            for (LotteryItem item : codeGrantItems) {
                String result = checkSingleItemCodeStock(activityId, item.getId(), item.getName());
                if (StringUtils.hasText(result)) {
                    errorMessages.add(result); // 收集错误信息，继续校验其他奖项
                }
            }
            return errorMessages;

        } catch (Exception e) {
            log.error("校验券码库存一致性异常，活动ID: {}", activityId, e);
        }
        return Collections.emptyList();
    }

    /**
     * 获取需要发放券码的奖项列表
     */
    private List<LotteryItem> getCodeGrantItems(Long activityId) {
        // 1. 查询活动下所有奖品的奖项ID
        Set<Long> activityItemIds = getActivityItemIds(activityId);

        if (CollectionUtils.isEmpty(activityItemIds)) {
            return Collections.emptyList();
        }

        // 2. 批量查询奖项信息，过滤出需要发放券码的奖项
        return lotteryItemService.list(new LambdaQueryWrapper<LotteryItem>().in(LotteryItem::getId, activityItemIds).eq(LotteryItem::getCodeGrantType, 1) // 1-发放券码
                .select(LotteryItem::getId, LotteryItem::getName, LotteryItem::getCodeGrantType));
    }

    /**
     * 获取活动下所有奖品的奖项ID
     */
    private Set<Long> getActivityItemIds(Long activityId) {
        // 从奖品实例中收集
        List<LotteryPrize> prizes = lotteryPrizeService.list(new LambdaQueryWrapper<LotteryPrize>().eq(LotteryPrize::getActivityId, activityId).select(LotteryPrize::getItemId));
        return prizes.stream().map(LotteryPrize::getItemId).collect(Collectors.toSet());
    }

    /**
     * 校验单个奖项的券码数量与库存一致性
     */
    private String checkSingleItemCodeStock(Long activityId, Long itemId, String itemName) {
        try {
            // 1. 统计该奖项在所有时间段的总库存
            int totalStock = calculateTotalStock(activityId, itemId);

            // 2. 统计该奖项的券码总数量
            int totalCodeCount = calculateTotalCodeCount(activityId, itemId);

            // 3. 比较库存与券码数量
            if (totalStock > totalCodeCount) {
                return String.format("物品[%s]的库存数量(%d)与券码数量(%d)不一致", itemName, totalStock, totalCodeCount);
            }

            log.info("奖项[{}]库存与券码数量校验通过，数量: {}", itemName, totalStock);
            return null; // 校验通过

        } catch (Exception e) {
            log.error("校验奖项{}库存与券码一致性异常", itemName, e);

        }
        return null;
    }

    /**
     * 计算奖项在活动中的总库存
     */
    @Override
    public int calculateTotalStock(Long activityId, Long itemId) {

        List<Long> prizeIds = lotteryPrizeService.list(new LambdaQueryWrapper<LotteryPrize>().eq(LotteryPrize::getActivityId, activityId).eq(LotteryPrize::getItemId, itemId).select(LotteryPrize::getId)).stream().map(LotteryPrize::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(prizeIds)){
            return 0;
        }
        // 查询该奖项在活动中所有时间段的库存总和
        List<LotteryPrizeTimePeriod> timePeriods = lotteryPrizeTimePeriodService.list(new LambdaQueryWrapper<LotteryPrizeTimePeriod>().in(LotteryPrizeTimePeriod::getPrizeId, prizeIds).select(LotteryPrizeTimePeriod::getAmount));

        return timePeriods.stream().mapToInt(LotteryPrizeTimePeriod::getAmount).sum();
    }

    /**
     * 计算奖项的券码总数量
     */
    private int calculateTotalCodeCount(Long activityId, Long itemId) {
        // 统计该活动该奖项的券码总数
        return Math.toIntExact(lotteryItemCodeService.count(new LambdaQueryWrapper<LotteryItemCode>().eq(LotteryItemCode::getActivityId, activityId).eq(LotteryItemCode::getItemId, itemId)));
    }

    /**
     * 校验LotteryItemConfig的bindMobileFlag配置（返回所有错误）
     */
    private List<String> checkBindMobileFlagConfigAll(Long activityId) {
        List<String> errorMessages = new ArrayList<>();

        try {
            // 查询活动下所有的LotteryItemConfig配置
            List<LotteryItemConfig> configs = lotteryItemConfigService.list(new LambdaQueryWrapper<LotteryItemConfig>().eq(LotteryItemConfig::getActivityId, activityId));

            if (CollectionUtils.isEmpty(configs)) {
                log.info("活动{}没有LotteryItemConfig配置，跳过bindMobileFlag校验", activityId);
                return Collections.emptyList();
            }

            // 检查是否有bindMobileFlag为null的配置
            List<LotteryItemConfig> invalidConfigs = configs.stream().filter(config -> Objects.isNull(config.getBindMobileFlag()) || Objects.isNull(config.getBindNameFlag())).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(invalidConfigs)) {
                // 获取对应的奖项名称用于错误提示
                Set<Long> invalidItemIds = invalidConfigs.stream().map(LotteryItemConfig::getItemId).collect(Collectors.toSet());

                List<String> itemNames = getItemNamesByIds(invalidItemIds);

                errorMessages.add(String.format("以下物品的配置为空: %s", String.join(", ", itemNames)));
            }

            if (errorMessages.isEmpty()) {
                log.info("活动{}的LotteryItemConfig配置校验通过，配置数量: {}", activityId, configs.size());
                return Collections.emptyList();
            }

            return errorMessages;

        } catch (Exception e) {
            log.error("校验LotteryItemConfig配置异常，活动ID: {}", activityId, e);
        }
        return Collections.emptyList();
    }

    /**
     * 根据奖项ID列表获取奖项名称列表
     */
    private List<String> getItemNamesByIds(Set<Long> itemIds) {
        if (CollectionUtils.isEmpty(itemIds)) {
            return Collections.emptyList();
        }

        return lotteryItemService.list(new LambdaQueryWrapper<LotteryItem>().in(LotteryItem::getId, itemIds).select(LotteryItem::getId, LotteryItem::getName)).stream().map(LotteryItem::getName).collect(Collectors.toList());
    }

}
