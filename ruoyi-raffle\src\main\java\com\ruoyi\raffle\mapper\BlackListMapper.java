package com.ruoyi.raffle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.raffle.domain.BlackList;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 黑名单数据访问层
 * 提供黑名单相关的数据库操作方法
 * 
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface BlackListMapper extends BaseMapper<BlackList> {

    /**
     * 根据openId查询黑名单记录
     *
     * @param openId 微信用户唯一标识
     * @return 黑名单记录，如果不存在则返回null
     */
    BlackList selectByOpenId(@Param("openId") String openId);


    /**
     * 软删除黑名单记录
     *
     * @param ids 黑名单记录id
     * @param updateUserId 更新用户id
     * @param updateUserName 更新用户名
     * @return 影响行数
     */
    int softDeleteByIds(@Param("ids") List<Long> ids,
                       @Param("updateUserId") Long updateUserId,
                       @Param("updateUserName") String updateUserName);
}
