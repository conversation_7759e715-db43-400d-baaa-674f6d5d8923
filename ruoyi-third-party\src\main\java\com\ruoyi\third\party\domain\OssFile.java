package com.ruoyi.third.party.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.math.BigDecimal;


@Data
public class OssFile {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 文件类型：1-图片，2-音频，3-视频
     */
    private Integer type;
    /**
     * 文件名含路径
     */
    private String name;
    /**
     * bucket
     */
    private String bucket;
    /**
     * 是否已上传
     */
    private Boolean upload;
    /**
     * 文件高度
     */
    private BigDecimal height;
    /**
     * 文件宽度
     */
    private BigDecimal width;
    /**
     * 文件大小
     */
    private BigDecimal size;


    @TableLogic
    private Boolean isDeleted;
}
