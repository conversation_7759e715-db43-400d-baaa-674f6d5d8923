package com.ruoyi.tool;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description redis分布式锁
 * @date 2021/12/02 9:03
 */
@Slf4j
@Component
public class RedisLock {
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 加锁，自旋重试十次
     *
     * @return
     */
    public boolean lock(String lockKey, String lockValue, Integer time, TimeUnit unit) {
        boolean locked = false;
        int tryCount = 20;
        while (!locked && tryCount > 0) {
            locked = redisTemplate.opsForValue().setIfAbsent(lockKey, lockValue, time, unit);
            tryCount--;
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                log.error("【RedisLock】线程被中断" + Thread.currentThread().getId(), e);
            }
        }
        return locked;
    }


    /**
     * 使用lua脚本解锁，不会解除别人锁
     *
     * @return
     */
    public boolean unlockLua(String lockKey, String lockValue) {
        if (StringUtils.isEmpty(lockKey) || StringUtils.isEmpty(lockValue)) {
            return false;
        }
        DefaultRedisScript<Long> redisScript = new DefaultRedisScript();
        //用于解锁的lua脚本位置
        redisScript.setLocation(new ClassPathResource("unlock.lua"));
        redisScript.setResultType(Long.class);
        //没有指定序列化方式，默认使用上面配置的
        Object result = redisTemplate.execute(redisScript, Arrays.asList(lockKey), lockValue);
        return result.equals(Long.valueOf(1));
    }
}
