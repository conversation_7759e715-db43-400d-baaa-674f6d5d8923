FROM registry.cn-shenzhen.aliyuncs.com/jsp-public/jre8-skywalking-agent:8.11.0

ENV TZ=Asia/Shanghai
ENV JAVA_OPTS="-XX:+UseContainerSupport -XX:MaxRAMPercentage=75.0 -XX:+HeapDumpOnOutOfMemoryError -Djava.security.egd=file:/dev/./urandom"

VOLUME /home/<USER>
WORKDIR /home

ENV SW_AGENT_NAME=prod::bflow-api
ENV SW_PLUGIN_SPRINGMVC_COLLECT_HTTP_PARAMS=false
ENV SW_JDBC_TRACE_SQL_PARAMETERS=true
ENV SW_AGENT_COLLECTOR_BACKEND_SERVICES=apm-skywalking-api-svc-na1g5.devops:11800
ENV SW_AGENT_SAMPLE=0
ENV SW_AGENT_TRACE_IGNORE_PATH=/actuator/health,Lettuce/INFO

EXPOSE 80


ADD ./ruoyi-admin/target/*.jar /home/<USER>
RUN bash -c 'touch /home/<USER>'

ENTRYPOINT  java $JAVA_OPTS -jar app.jar --server.port=80 --spring.profiles.active=$ENV
