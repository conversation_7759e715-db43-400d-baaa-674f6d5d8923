package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.raffle.domain.LotteryItemCode;
import com.ruoyi.raffle.domain.LotteryLog;
import com.ruoyi.raffle.domain.LotteryPrizeTimePeriod;
import com.ruoyi.raffle.mapper.LotteryLogMapper;
import com.ruoyi.raffle.service.ILotteryItemCodeService;
import com.ruoyi.raffle.service.ILotteryLogService;
import com.ruoyi.raffle.service.ILotteryPrizeTimePeriodService;
import com.ruoyi.third.party.domain.dto.request.jsp.JspLotteryOperateRequest;
import com.ruoyi.third.party.domain.dto.response.jsp.JspLotteryOperateResponse;
import com.ruoyi.third.party.service.impl.JspServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 抽奖日志表 服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class LotteryLogServiceImpl extends ServiceImpl<LotteryLogMapper, LotteryLog> implements ILotteryLogService {

    @Resource
    private ILotteryPrizeTimePeriodService lotteryPrizeTimePeriodService;

    @Resource
    private ILotteryItemCodeService lotteryItemCodeService;

    @Resource
    private JspServiceImpl jspService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveLotteryInfo(LotteryLog lotteryLog, Long prizeTimePeriodId,String title) {
        if (Objects.nonNull(prizeTimePeriodId)) {
            LambdaUpdateWrapper<LotteryPrizeTimePeriod> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(LotteryPrizeTimePeriod::getId, prizeTimePeriodId)
                    .setSql("issue_amount = issue_amount + 1");
            boolean update = lotteryPrizeTimePeriodService.update(updateWrapper);
            if (!update){
                log.error("更新发放数量失败");
                throw new ServiceException(-100, "活动太火爆了，请稍后再试!");
            }
        }
        if(StringUtils.hasText(lotteryLog.getCouponCode())){
            LambdaUpdateWrapper<LotteryItemCode> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(LotteryItemCode::getActivityId, lotteryLog.getActivityId())
                    .eq(LotteryItemCode::getItemId, lotteryLog.getItemId())
                    .eq(LotteryItemCode::getCode, lotteryLog.getCouponCode())
                    .eq(LotteryItemCode::getGrantType, 0)
                    .set(LotteryItemCode::getGrantType, 1)
                    .set(LotteryItemCode::getUpdateDate, DateUtils.getNowDate());
            boolean update = lotteryItemCodeService.update(updateWrapper);
            if (!update){
                log.error("更新券码状态失败");
                throw new ServiceException(-100, "活动太火爆了，请稍后再试!");
            }
        }
        lotteryLog.setCreateDate(DateUtils.getNowDate());
        boolean save = this.save(lotteryLog);
        if (!save){
            log.error("保存抽奖记录失败");
            throw new ServiceException(-100, "活动太火爆了，请稍后再试!");
        }

        //发放奖励
        if (Objects.equals(1, lotteryLog.getItemType()) || Objects.equals(2, lotteryLog.getItemType())) {
            JspLotteryOperateRequest operateRequest = new JspLotteryOperateRequest();
            operateRequest.setActivityId(lotteryLog.getId());
            operateRequest.setType(Objects.equals(1, lotteryLog.getItemType()) ? 2: 1 );
            operateRequest.setOperateType(1);
            operateRequest.setPrice(lotteryLog.getItemVal());
            operateRequest.setLogId(lotteryLog.getId());
            operateRequest.setTitle(title);
            this.lotteryOperateJsp(operateRequest);
        }

        return lotteryLog.getId();
    }

    /**
     * 获取活动信息
     */
    @Override
    public JspLotteryOperateResponse lotteryOperateJsp(JspLotteryOperateRequest operateRequest) {
        JspLotteryOperateResponse response = null;
        try {
            response = jspService.lotteryOperate(SecurityUtils.getTicket(), SecurityUtils.getOpenId(), operateRequest);
        } catch (Exception e) {
            throw new ServiceException(-100, "活动太火爆了，请稍后再试!");
        }
        if (Objects.nonNull(response)) {
            if (response.getCode() != 0) {
                throw new ServiceException(response.getCode(), response.getMessage());
            }
        }else{
            throw new ServiceException(-100, "活动太火爆了，请稍后再试!");
        }
        return response;
    }
}