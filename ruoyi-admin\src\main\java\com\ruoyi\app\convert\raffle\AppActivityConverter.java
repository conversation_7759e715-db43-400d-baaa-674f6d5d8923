package com.ruoyi.app.convert.raffle;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.app.model.raffle.ActivityMemberVO;
import com.ruoyi.app.model.raffle.AppActivityVO;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.raffle.domain.ActivityMemberAddress;
import com.ruoyi.raffle.service.IActivityMemberAddressService;
import com.ruoyi.third.party.service.IOssFileService;
import com.ruoyi.raffle.domain.Activity;
import com.ruoyi.raffle.domain.ActivityMember;
import com.ruoyi.raffle.domain.RaffleUser;
import com.ruoyi.raffle.service.IActivityMemberService;
import com.ruoyi.raffle.service.IRaffleUserService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class AppActivityConverter {

    @Resource
    private IActivityMemberService activityMemberService;
    @Resource
    private IRaffleUserService raffleUserService;
    @Resource
    private IOssFileService ossFileService;
    @Resource
    private IActivityMemberAddressService activityMemberAddressService;

    public AppActivityVO convert(Activity activity, LoginUser loginUser) {
        SysUser sysUser = Objects.isNull(loginUser) ? null : loginUser.getUser();
        return this.convert(activity, sysUser);
    }


    public AppActivityVO convert(Activity activity, SysUser sysUser) {
        AppActivityVO activityVO = new AppActivityVO();
        activityVO.setId(activity.getId());
        activityVO.setActivityName(activity.getActivityName());
        activityVO.setActivityRule(activity.getActivityRule());
        activityVO.setActivityDescription(activity.getActivityDescription());
        activityVO.setActivityNum(activity.getActivityNum());
        activityVO.setRaffleOpenTime(activity.getRaffleOpenTime());
        activityVO.setActivityPromoPic(activity.getActivityPromoPic());
        activityVO.setActivityPromoPicUrl(ossFileService.getOriUrl(activity.getActivityPromoPic()));
        activityVO.setBgColor(activity.getBgColor());
        activityVO.setActivityPrize(activity.getActivityPrize());
        activityVO.setActivityPrizeNum(activity.getActivityPrizeNum());
        activityVO.setActivityPrizeValue(activity.getActivityPrizeValue());
        activityVO.setActivityPrizePic(activity.getActivityPrizePic());
        activityVO.setActivityPrizePicUrl(ossFileService.getOriUrl(activity.getActivityPrizePic()));
        activityVO.setActivityPrizeNeedSent(activity.getActivityPrizeNeedSent());
        activityVO.setActivityUrl(activity.getActivityUrl());
        activityVO.setRaffleStatus(activity.getRaffleStatus());
        activityVO.setStatus(activity.getStatus());
        activityVO.setTenantId(activity.getTenantId());
        activityVO.setCreateId(activity.getCreateId());
        activityVO.setCreateName(activity.getCreateName());
        activityVO.setCreateDate(activity.getCreateDate());
        activityVO.setUpdateId(activity.getUpdateId());
        activityVO.setUpdateName(activity.getUpdateName());
        activityVO.setUpdateDate(activity.getUpdateDate());
        activityVO.setIsDeleted(activity.getIsDeleted());
        activityVO.setIsDarkFont(activity.getIsDarkFont());

        //  补充当前用户抽奖状态
        if (Objects.nonNull(sysUser)) {
            ActivityMember activityMember = activityMemberService.getByActivityIdAndUserId(activity.getId(), sysUser.getUserId());
            if (Objects.nonNull(activityMember)) {
                activityVO.setUserJoinStatus(1);
                activityVO.setUserRaffleStatus(activityMember.getRaffleStatus());
                ActivityMemberAddress memberAddress = activityMemberAddressService.getByMemberId(activityMember.getId());
                activityVO.setUserAddress(memberAddress);
            } else {
                activityVO.setUserJoinStatus(0);
                activityVO.setUserRaffleStatus(0);
            }

            RaffleUser raffleUser = raffleUserService.getBySysUserId(sysUser.getUserId());
            if (Objects.nonNull(raffleUser)) {
                activityVO.setUserNickname(raffleUser.getNickname());
                activityVO.setUserAvatar(raffleUser.getAvatar());
            } else {
                activityVO.setUserAvatar("");
                activityVO.setUserNickname("");
            }


        } else {
            activityVO.setUserJoinStatus(0);
            activityVO.setUserRaffleStatus(0);
            activityVO.setUserAvatar("");
            activityVO.setUserNickname("");
        }

        //  活动参与用户信息

        //  参与人数
        activityVO.setApplyCount(activityMemberService.getApplyCount(activity.getId()));

        //  参与活动前3用户头像
        LambdaQueryWrapper<ActivityMember> applyMemberQuery = Wrappers.lambdaQuery();
        applyMemberQuery.eq(ActivityMember::getActivityId, activity.getId());
        applyMemberQuery.eq(ActivityMember::getStatus, 1);
        applyMemberQuery.last(" limit 3");
        List<ActivityMember> applyMembers = activityMemberService.list(applyMemberQuery);
        activityVO.setApplyUserAvatars(applyMembers.stream().map(ActivityMember::getAvatar).collect(Collectors.toList()));

        //  中奖用户列表
        LambdaQueryWrapper<ActivityMember> winMemberQuery = Wrappers.lambdaQuery();
        winMemberQuery.eq(ActivityMember::getActivityId, activity.getId());
        winMemberQuery.eq(ActivityMember::getRaffleStatus, 1);
        winMemberQuery.eq(ActivityMember::getStatus, 1);
        List<ActivityMember> winMembers = activityMemberService.list(winMemberQuery);
        activityVO.setWinUsers(winMembers.stream().map(this::convert).collect(Collectors.toList()));

        return activityVO;
    }

    public ActivityMemberVO convert(ActivityMember member) {
        ActivityMemberVO activityMemberVO = new ActivityMemberVO();
        activityMemberVO.setId(member.getId());
        activityMemberVO.setUserId(member.getUserId());
        activityMemberVO.setNickname(member.getNickName());
        activityMemberVO.setAvatar(member.getAvatar());
        return activityMemberVO;
    }


}
