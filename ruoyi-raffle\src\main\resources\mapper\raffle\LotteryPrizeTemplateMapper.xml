<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!--
    抽奖奖品模板表数据访问层映射文件
    提供奖品模板相关的SQL映射配置
    
    <AUTHOR>
    @date 2023-12-18
-->
<mapper namespace="com.ruoyi.raffle.mapper.LotteryPrizeTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.raffle.domain.LotteryPrizeTemplate">
        <id column="id" property="id" />
        <result column="activity_id" property="activityId" />
        <result column="item_id" property="itemId" />
        <result column="amount" property="amount" />
        <result column="prize_type" property="prizeType" />
        <result column="user_strategy" property="userStrategy" />
        <result column="device_strategy" property="deviceStrategy" />
        <result column="mutually_item_ids" property="mutuallyItemIds" />
        <result column="sort" property="sort" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_date" property="createDate" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_date" property="updateDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activity_id, item_id, amount, prize_type, user_strategy, device_strategy, 
        mutually_item_ids, sort, create_user_id, create_user_name, create_date, 
        update_user_id, update_user_name, update_date
    </sql>
    <select id="queryTemplateInfoList" resultType="com.ruoyi.raffle.domain.dto.LotteryTemplateInfoDTO">
        SELECT
            t1.id,
            t1.item_id itemId,
            t1.prize_type prizeType,
            t1.user_strategy userStrategy,
            t1.device_strategy deviceStrategy,
            t1.mutually_item_ids mutuallyItemIds,
            t1.sort,
            t2.type,
            t2.`name` name,
            t2.cover
        FROM
            lottery_prize_template t1 left join lottery_item t2 on t1.item_id = t2.id
        WHERE
            t1.activity_id = #{activityId}
        order by t1.prize_type,t1.sort asc



    </select>


</mapper>
