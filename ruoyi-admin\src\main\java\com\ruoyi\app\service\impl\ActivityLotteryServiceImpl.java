package com.ruoyi.app.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ruoyi.app.model.lottery.LotteryActivityInfoVO;
import com.ruoyi.app.model.lottery.LotteryCompleteWinningVO;
import com.ruoyi.app.model.lottery.LotteryResultVO;
import com.ruoyi.app.service.ActivityLotteryService;
import com.ruoyi.common.constant.JspRedisConstants;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.ip.AddressUtils;
import com.ruoyi.common.utils.ip.IpUtils;
import com.ruoyi.raffle.domain.LotteryActivity;
import com.ruoyi.raffle.domain.LotteryItem;
import com.ruoyi.raffle.domain.LotteryItemCode;
import com.ruoyi.raffle.domain.LotteryItemConfig;
import com.ruoyi.raffle.domain.LotteryLog;
import com.ruoyi.raffle.domain.dto.LotteryPrizeDTO;
import com.ruoyi.raffle.service.IBlackListService;
import com.ruoyi.raffle.service.ILotteryActivityService;
import com.ruoyi.raffle.service.ILotteryItemConfigService;
import com.ruoyi.raffle.service.ILotteryItemService;
import com.ruoyi.raffle.service.ILotteryLogService;
import com.ruoyi.raffle.service.ILotteryPrizeService;
import com.ruoyi.third.party.domain.dto.request.jsp.JspLotteryOperateRequest;
import com.ruoyi.third.party.service.IJspService;
import com.ruoyi.third.party.service.IOssFileService;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: niedamin
 * @Date: 2023/04/20 17:42
 */
@Slf4j
@Service
public class ActivityLotteryServiceImpl implements ActivityLotteryService {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private ILotteryPrizeService lotteryPrizeService;

    @Resource
    private ILotteryActivityService lotteryActivityService;

    @Resource
    private ILotteryLogService lotteryLogService;

    @Resource
    private ILotteryItemService lotteryItemService;

    @Resource
    private IJspService jspService;

    @Resource
    private IOssFileService ossFileService;

    @Resource
    private IBlackListService blackListService;

    @Resource
    private ILotteryItemConfigService lotteryItemConfigService;

    @Override
    public R<LotteryResultVO> lottery(Long activityId, LoginUser loginUser) {

        Long userId = loginUser.getUserId();
        String ipAddr = IpUtils.getIpAddr();
        String ipLocation = AddressUtils.getRealAddressByIP(ipAddr);
        String deviceId = loginUser.getDeviceId();

        LotteryActivity lotteryActivity = this.getActivityById(activityId);

        LocalDateTime now = LocalDateTime.now();
        String activityDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 当日可抽奖数
        int dailyMaxLotteryCount = this.getDailyMaxLotteryCount(lotteryActivity, userId);
        // 当日已抽奖数
        int dailyLotteryCount = this.getDailyLotteryCount(activityId, userId, activityDate);

        // 当日剩余可抽奖次数
        int dailyRemainderLotteryCount = dailyMaxLotteryCount - dailyLotteryCount;

        if (dailyRemainderLotteryCount <= 0) {
            return R.fail(-1, "快去做任务获得抽奖次数吧");
        }

        // 获取当天配置的奖品列表
        List<LotteryPrizeDTO> prizeDTOList = this.getLotteryPrizeList(activityId, activityDate);
        // 保底奖品
        LotteryPrizeDTO breakEvenPrizeDTO = prizeDTOList.stream().filter(e -> Objects.equals(1, e.getPrizeType())).findFirst().orElse(null);
        if (Objects.isNull(breakEvenPrizeDTO)) {
            return R.fail(-1, "奖品列表为空");
        }


        int depleteType = Objects.isNull(lotteryActivity.getDepleteBeanType()) ? 0 : lotteryActivity.getDepleteBeanType();
        int depleteBeans = (Objects.isNull(lotteryActivity.getDepleteBean()) || lotteryActivity.getDepleteBean() <= 0) ? 0 : lotteryActivity.getDepleteBean();

        // 抽奖需要消耗今豆
        if (depleteType > 0 && depleteBeans > 0) {

            if (Objects.equals(1, depleteType) || (Objects.equals(2, depleteType) && dailyLotteryCount == 0)) {
                // 抽奖扣今豆
                JspLotteryOperateRequest operateRequest = new JspLotteryOperateRequest();
                operateRequest.setActivityId(lotteryActivity.getId());
                operateRequest.setType(1);
                operateRequest.setOperateType(2);
                operateRequest.setPrice(BigDecimal.valueOf(depleteBeans));
                operateRequest.setLogId(0L);
                operateRequest.setTitle(lotteryActivity.getTitle());
                this.lotteryLogService.lotteryOperateJsp(operateRequest);
            }
        }

        // 是否是黑名单
        boolean isLotteryBlack = blackListService.isInBlackList(SecurityUtils.getOpenId());

        // 限制ip抽奖 0-不限制 1-限制
        int limitIpAddr = Objects.isNull(lotteryActivity.getLimitIpAddr()) ? 0 : lotteryActivity.getLimitIpAddr();
        boolean isLotteryIpLimit = (limitIpAddr == 0 || (limitIpAddr > 0 && StringUtils.hasText(ipLocation) && ipLocation.contains("江西")));
        boolean isDeviceLimit = false;

        if (StringUtils.hasText(deviceId)) {
            int deviceLimit = (Objects.isNull(lotteryActivity.getDeviceLimit()) || lotteryActivity.getDeviceLimit() <= 0) ? 5 : lotteryActivity.getDeviceLimit();
            String deviceKey = this.getDailyDeviceKey(activityId, deviceId);
            int dailyDeviceCount = this.getDailyDeviceCount(deviceKey);
            isDeviceLimit = (dailyDeviceCount >= deviceLimit);
        }
        // 抽中的奖品
        LotteryPrizeDTO prizeDTO = null;
        if (isLotteryIpLimit && !isLotteryBlack && !isDeviceLimit) {
            // 优先抽中的奖品列表
            List<LotteryPrizeDTO> priorityPrizeDTOList = prizeDTOList.stream().filter(e -> (e.getAmount() > e.getIssueAmount()) && Objects.equals(0, e.getPrizeType())).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(priorityPrizeDTOList)) {

                Boolean isCredible = stringRedisTemplate.opsForSet().isMember("raffle:activity:lottery:credible:ids", String.valueOf(userId));
                if (Boolean.TRUE.equals(isCredible) && priorityPrizeDTOList.stream().anyMatch(e -> !Objects.equals(e.getType(), 2) && e.getAmount() - e.getIssueAmount() > 0)) {
                    priorityPrizeDTOList.removeIf(e -> Objects.equals(e.getType(), 2));
                    Collections.shuffle(priorityPrizeDTOList);
                    prizeDTO = priorityPrizeDTOList.get(0);
                } else {
                    /*
                     * int priorityCount = priorityPrizeDTOList.stream().mapToInt(e -> e.getAmount() - e.getIssueAmount()).sum(); List<Long> prizeIdList = new ArrayList<>(priorityCount); priorityPrizeDTOList.forEach(e -> { for (int i = 0; i < e.getAmount() - e.getIssueAmount(); i++) { prizeIdList.add(e.getId()); } });
                     * 
                     * // 打乱顺序 Collections.shuffle(prizeIdList); // 抽取第一个id Long prizeId = prizeIdList.get(0); prizeDTO = priorityPrizeDTOList.stream().filter(e -> Objects.equals(prizeId, e.getId())).findFirst().orElse(null);
                     */
                    if (Objects.equals(1, lotteryActivity.getType())) {
                        prizeDTO = this.lotteryByStock(priorityPrizeDTOList);
                    } else {
                        prizeDTO = this.lotteryByProbability(priorityPrizeDTOList);
                    }
                }


            }
        }

        // 查看抽中的奖品是否有限制
        if (Objects.nonNull(prizeDTO)) {
            prizeDTO = checkLotteryStrategy(prizeDTO, activityId, userId, deviceId);
        }


        // 没有抽中优先奖励，则发放保底奖励
        if (Objects.isNull(prizeDTO)) {
            prizeDTO = breakEvenPrizeDTO;
        }

        LotteryLog lotteryLog = new LotteryLog();
        lotteryLog.setActivityId(lotteryActivity.getId());
        lotteryLog.setUserId(userId);
        lotteryLog.setDeviceId(deviceId);
        lotteryLog.setIpAddr(ipAddr);
        lotteryLog.setIpLocation(ipLocation);
        lotteryLog.setIssueDate(com.ruoyi.common.utils.DateUtils.getNowDate());
        lotteryLog.setPrizeId(prizeDTO.getId());
        lotteryLog.setItemType(prizeDTO.getType());
        lotteryLog.setItemId(prizeDTO.getItemId());
        lotteryLog.setItemName(prizeDTO.getName());
        lotteryLog.setPrizeType(prizeDTO.getPrizeType());
        BigDecimal randomPrize = randomBetween(prizeDTO.getPrizeMin(), prizeDTO.getPrizeMax());

        if (Objects.equals(2, prizeDTO.getType())) {
            lotteryLog.setItemVal(new BigDecimal(String.valueOf(randomPrize.intValue())));
        } else {
            lotteryLog.setItemVal(randomPrize);
        }
        if (Objects.equals(1, prizeDTO.getCodeGrantType())) {
            LotteryItemCode lotteryItemCode = lotteryItemService.queryCouponCodeByItemId(prizeDTO.getItemId());
            if (Objects.isNull(lotteryItemCode)) {
                throw new ServiceException(-1, "券码不存在");
            }
            lotteryLog.setCouponCode(lotteryItemCode.getCode());
        }
        LotteryItemConfig itemConfig = null;
        if (Objects.equals(3, lotteryLog.getItemType())) {
            itemConfig = this.getItemConfig(activityId, prizeDTO.getItemId());
            if (Objects.nonNull(itemConfig) && Objects.equals(2, itemConfig.getBindMobileFlag())) {
                lotteryLog.setBindMobile(SecurityUtils.getMobile());
            }
        }

        // 扣减库存
        Long logId = this.lotteryLogService.saveLotteryInfo(lotteryLog, prizeDTO.getPeriodId(), lotteryActivity.getTitle());
        /*
         * if (Objects.isNull(logId)) { return R.fail(-2, "活动太火爆了，请稍后再试!"); }
         */
        // 发放奖励
        /*
         * if (Objects.equals(1, lotteryLog.getItemType()) || Objects.equals(2, lotteryLog.getItemType())) { JspLotteryOperateRequest operateRequest = new JspLotteryOperateRequest(); operateRequest.setActivityId(lotteryActivity.getId()); operateRequest.setType(Objects.equals(1, lotteryLog.getItemType()) ? 2: 1 ); operateRequest.setOperateType(1); operateRequest.setPrice(lotteryLog.getItemVal()); operateRequest.setLogId(lotteryLog.getId()); operateRequest.setTitle(lotteryActivity.getTitle()); this.lotteryOperateJsp(operateRequest); }
         */


        if (StringUtils.hasText(deviceId)) {
            String deviceKey = this.getDailyDeviceKey(activityId, deviceId);
            // 增加设备的抽奖次数
            stringRedisTemplate.opsForValue().increment(deviceKey);
            // 设置过期时间，例如24小时
            stringRedisTemplate.expire(deviceKey, 24, TimeUnit.HOURS);
        }

        LotteryResultVO vo = new LotteryResultVO();
        vo.setId(prizeDTO.getId());
        vo.setItemId(lotteryLog.getItemId());
        vo.setType(lotteryLog.getItemType());
        vo.setName(lotteryLog.getItemName());
        vo.setPrizeVal(Objects.equals(1, lotteryLog.getItemType()) ? lotteryLog.getItemVal().toPlainString() : lotteryLog.getItemVal().stripTrailingZeros().toPlainString());
        vo.setLogId(lotteryLog.getId());
        vo.setCouponCode(lotteryLog.getCouponCode());
        vo.setBindMobile(lotteryLog.getBindMobile());
        // vo.setPrizeId(prizeId);
        if (StringUtils.hasText(prizeDTO.getCover())) {
            vo.setCoverUrl(ossFileService.getOriUrl(prizeDTO.getCover()));
        }
        vo.setBindMobileFlag(Objects.nonNull(itemConfig) ? itemConfig.getBindMobileFlag() : 0);
        vo.setBindNameFlag(Objects.nonNull(itemConfig) ? itemConfig.getBindNameFlag() : 0);
        return R.ok(vo);
    }

    @Override
    public R<LotteryActivityInfoVO> getActivityInfoById(Long activityId, Long userId) {
        LotteryActivity lotteryActivity = this.getActivityById(activityId);

        LocalDateTime now = LocalDateTime.now();
        String activityDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        List<LotteryPrizeDTO> prizeDTOList = this.lotteryPrizeService.queryPrizePools(activityId, activityDate);

        // 当日可抽奖数
        int dailyMaxLotteryCount = this.getDailyMaxLotteryCount(lotteryActivity, userId);
        // 当日已抽奖数
        int dailyLotteryCount = this.getDailyLotteryCount(activityId, userId, activityDate);

        // 获取活动的每日分享最大次数配置
        int maxShareCount = (Objects.isNull(lotteryActivity.getShareMaxAmount()) || lotteryActivity.getShareMaxAmount() <= 0) ? 0 : lotteryActivity.getShareMaxAmount();

        String shareCountKey = this.getDailyShareKey(activityId, userId);
        int currentShareCount = this.getDailyShareCount(shareCountKey);

        LotteryActivityInfoVO vo = new LotteryActivityInfoVO();
        vo.setStartTime(lotteryActivity.getStartDate().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        vo.setEndTime(lotteryActivity.getEndDate().toInstant(ZoneOffset.of("+8")).toEpochMilli());
        vo.setIsEndDay(lotteryActivity.getEndDate().toLocalDate().isEqual(now.toLocalDate()));
        vo.setLotteryAmount(dailyMaxLotteryCount - dailyLotteryCount);
        vo.setShareAmount(maxShareCount - currentShareCount);

        List<LotteryLog> couponLogList = this.getLotteryLogList(activityId, userId, null, 3, null);
        if (!CollectionUtils.isEmpty(couponLogList)) {

            List<LotteryActivityInfoVO.GoodsInfo> collect = couponLogList.stream().map(logDTO -> {
                LotteryActivityInfoVO.GoodsInfo goodsInfo = new LotteryActivityInfoVO.GoodsInfo();
                goodsInfo.setLogId(logDTO.getId());
                goodsInfo.setName(logDTO.getItemName());
                if (Objects.nonNull(logDTO.getItemId())) {
                    goodsInfo.setCoverUrl(ossFileService.getOriUrl(this.lotteryItemService.getById(logDTO.getItemId()).getCover()));
                }
                goodsInfo.setIssueTime(logDTO.getIssueDate());
                goodsInfo.setCouponCode(logDTO.getCouponCode());
                goodsInfo.setBindMobile(logDTO.getBindMobile());
                goodsInfo.setBindName(logDTO.getBindName());

                LotteryItemConfig itemConfig = this.getItemConfig(activityId, logDTO.getItemId());
                if (Objects.nonNull(itemConfig)) {
                    goodsInfo.setBindMobileFlag(itemConfig.getBindMobileFlag());
                    goodsInfo.setBindNameFlag(itemConfig.getBindNameFlag());
                }

                return goodsInfo;

            }).collect(Collectors.toList());

            vo.setGoodsList(collect);
        }
        prizeDTOList.removeIf(e -> Objects.equals(1, e.getPrizeType()));
        vo.setPrizeList(prizeDTOList.stream().map(item -> {
            LotteryActivityInfoVO.PrizeDetail prizeInfo = new LotteryActivityInfoVO.PrizeDetail();
            prizeInfo.setId(item.getId());
            prizeInfo.setItemId(item.getItemId());
            prizeInfo.setType(item.getType());
            prizeInfo.setName(item.getName());
            if (StringUtils.hasText(item.getCover())) {
                prizeInfo.setCoverUrl(ossFileService.getOriUrl(item.getCover()));
            }
            prizeInfo.setSort(item.getSort());
            return prizeInfo;
        }).collect(Collectors.toList()));

        String activityInviteKey = this.getActivityInviteKey(activityId);
        // 获取用户邀请成功数
        int dailyInviteCount = this.getDailyInviteCount(activityInviteKey);
        vo.setInviteCount(dailyInviteCount);
        int watchRewardCount = this.getWatchRewardCount(lotteryActivity);
        vo.setWatchCount(watchRewardCount);
        vo.setIsJuniorMode(stringRedisTemplate.hasKey(String.format(JspRedisConstants.JSP_JUNIOR_KEY, SecurityUtils.getOpenId())));
        return R.ok(vo);
    }

    @Override
    public R<Integer> share(Long activityId, Long jid) {
        LotteryActivity lotteryActivity = this.getActivityById(activityId);

        if (Objects.isNull(lotteryActivity.getShareMaxAmount()) || lotteryActivity.getShareMaxAmount() <= 0) {
            return R.fail(-1, "活动分享未配置");
        }

        // 获取活动的每日分享最大次数配置
        Integer maxShareCount = lotteryActivity.getShareMaxAmount();

        String shareCountKey = this.getDailyShareKey(activityId, jid);
        int currentShareCount = this.getDailyShareCount(shareCountKey);

        // 判断是否超过最大分享次数
        if (currentShareCount >= maxShareCount) {
            return R.fail(-1, "已达到每日分享最大次数");
        }
        // 增加用户的分享次数
        stringRedisTemplate.opsForValue().increment(shareCountKey);
        // 设置过期时间，例如24小时
        stringRedisTemplate.expire(shareCountKey, 24, TimeUnit.HOURS);

        LocalDateTime now = LocalDateTime.now();
        String activityDate = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

        // 当日可抽奖数
        int dailyMaxLotteryCount = this.getDailyMaxLotteryCount(lotteryActivity, jid);
        // 当日已抽奖数
        int dailyLotteryCount = this.getDailyLotteryCount(activityId, jid, activityDate);

        return R.ok(dailyMaxLotteryCount - dailyLotteryCount);
    }



    @Override
    public R<Boolean> completeWinning(LotteryCompleteWinningVO vo, Long userId) {
        LotteryActivity lotteryActivity = this.getActivityById(vo.getActivityId());

        LotteryLog lotteryLog = this.lotteryLogService.getById(vo.getLogId());

        if (Objects.isNull(lotteryLog) || !Objects.equals(vo.getActivityId(), lotteryLog.getActivityId()) || !Objects.equals(userId, lotteryLog.getUserId())) {
            return R.fail(-1, "抽奖记录不存在!");
        }
        if (!Objects.equals(3, lotteryLog.getItemType())) {
            return R.ok(true);
        }

        LotteryItem lotteryItem = this.lotteryItemService.getById(lotteryLog.getItemId());
        if (Objects.isNull(lotteryItem)) {
            return R.fail(-1, "抽奖记录不存在!");
        }
        LotteryItemConfig itemConfig = this.getItemConfig(lotteryLog.getActivityId(), lotteryLog.getItemId());

        if (Objects.equals(itemConfig.getBindMobileFlag(), 0) && Objects.equals(itemConfig.getBindNameFlag(), 0)) {
            return R.ok();
        }

        if (Objects.equals(itemConfig.getBindMobileFlag(), 1) && !StringUtils.hasText(vo.getMobile())) {
            return R.fail(-1, "手机号不能为空!");
        }

        if (Objects.equals(itemConfig.getBindNameFlag(), 1) && !StringUtils.hasText(vo.getName())) {
            return R.fail(-1, "姓名不能为空!");
        }

        if ((Objects.equals(itemConfig.getBindMobileFlag(), 1) && StringUtils.hasText(lotteryLog.getBindMobile())) || (Objects.equals(itemConfig.getBindNameFlag(), 1) && StringUtils.hasText(lotteryLog.getBindName()))) {
            return R.fail(-1, "您已完善信息，不能再修改!");
        }

        if (Objects.equals(itemConfig.getBindMobileFlag(), 1) && Objects.equals(itemConfig.getMobileUniqueFlag(), 1)) {
            LambdaQueryWrapper<LotteryLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(LotteryLog::getActivityId, lotteryActivity.getId());
            queryWrapper.eq(LotteryLog::getItemType, 3);
            queryWrapper.eq(LotteryLog::getBindMobile, vo.getMobile());
            long count = this.lotteryLogService.count(queryWrapper);

            if (count > 0) {
                return R.fail(-1, "该手机号已被绑定，请更换!");
            }
        }
        lotteryLog.setBindMobile(vo.getMobile());
        lotteryLog.setBindName(vo.getName());
        boolean status = this.lotteryLogService.updateById(lotteryLog);
        return R.ok(status);
    }

    /**
     * 按库存抽奖
     * 
     * @param inviteKey
     * @return
     */
    private LotteryPrizeDTO lotteryByStock(List<LotteryPrizeDTO> prizeList) {
        if (CollectionUtils.isEmpty(prizeList)) {
            return null;
        }

        // 2. 计算总库存数
        int totalStock = prizeList.stream().mapToInt(p -> p.getAmount() - p.getIssueAmount()).sum();

        if (totalStock <= 0) {
            return null;
        }

        // 3. 随机选择一个奖品
        int randomIndex = new Random().nextInt(totalStock);
        int currentIndex = 0;

        LotteryPrizeDTO selectedPrize = null;
        for (LotteryPrizeDTO prize : prizeList) {
            int availableStock = prize.getAmount() - prize.getIssueAmount();
            currentIndex += availableStock;

            if (randomIndex < currentIndex) {
                selectedPrize = prize;
                break;
            }
        }

        if (selectedPrize == null) {
            return null;
        }
        return selectedPrize;
    }

    /**
     * 按概率抽奖
     * 
     * @param inviteKey
     * @return
     */
    private LotteryPrizeDTO lotteryByProbability(List<LotteryPrizeDTO> prizeList) {
        if (CollectionUtils.isEmpty(prizeList)) {
            return null;
        }

        // 2. 计算总库存数
        int totalStock = prizeList.stream().mapToInt(p -> p.getAmount() - p.getIssueAmount()).sum();

        if (totalStock <= 0) {
            return null;
        }

        // 2. 计算总概率(假设每个奖品有probability字段)
        double totalProbability = prizeList.stream().mapToDouble(LotteryPrizeDTO::getProbability).sum();

        // 确保总概率不超过1
        totalProbability = Math.min(totalProbability, 1.0);

        // 3. 生成随机数并判断是否中奖
        double randomValue = Math.random();
        if (randomValue > totalProbability) {
            return null; // 未中奖
        }

        // 4. 根据概率区间确定中奖奖品
        double accumulatedProbability = 0;
        LotteryPrizeDTO selectedPrize = null;

        for (LotteryPrizeDTO prize : prizeList) {
            accumulatedProbability += prize.getProbability();
            if (randomValue <= accumulatedProbability) {
                selectedPrize = prize;
                break;
            }
        }

        if (selectedPrize == null) {
            return null;
        }
        return selectedPrize;
    }


    /**
     * 获取用户邀请成功数
     *
     */
    private int getDailyInviteCount(String inviteKey) {
        String currentInviteCount = stringRedisTemplate.opsForValue().get(inviteKey);
        return StringUtils.hasText(currentInviteCount) ? Integer.parseInt(currentInviteCount) : 0;
    }

    /**
     * 获取用户今日分享次数
     */
    private int getDailyShareCount(String shareKey) {
        String currentShareCountStr = stringRedisTemplate.opsForValue().get(shareKey);
        return StringUtils.hasText(currentShareCountStr) ? Integer.parseInt(currentShareCountStr) : 0;
    }

    /**
     * 获取用户今日分享次数的Redis Key
     */
    private String getDailyShareKey(Long activityId, Long jid) {
        String LOTTERY_SHARE_KEY = "raffle:activity:lottery:share:%s:%s:%s";
        return String.format(LOTTERY_SHARE_KEY, activityId, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), jid);
    }

    /**
     * 获取用户今日设备抽奖次数
     */
    private int getDailyDeviceCount(String deviceKey) {
        String currentDeviceCountStr = stringRedisTemplate.opsForValue().get(deviceKey);
        return StringUtils.hasText(currentDeviceCountStr) ? Integer.parseInt(currentDeviceCountStr) : 0;
    }

    /**
     * 获取用户今日设备抽奖的Redis Key
     */
    private String getDailyDeviceKey(Long activityId, String deviceId) {
        String LOTTERY_DEVICE_KEY = "raffle:activity:lottery:%s:%s:device:%s";
        return String.format(LOTTERY_DEVICE_KEY, activityId, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), deviceId);
    }

    /**
     * 获取用户今日完成观看任务获得的抽奖次数
     */
    private int getWatchRewardCount(LotteryActivity lotteryActivity) {

        if (Objects.nonNull(lotteryActivity.getWatchDuration()) && lotteryActivity.getWatchDuration() > 0 && Objects.nonNull(lotteryActivity.getWatchFactor()) && lotteryActivity.getWatchFactor() > 0) {
            Integer watchDuration = lotteryActivity.getWatchDuration();
            Integer watchFactor = lotteryActivity.getWatchFactor();
            // TODO
            String watchStr = stringRedisTemplate.opsForValue().get(String.format(JspRedisConstants.JSP_BEAN_WATCH_KEY, SecurityUtils.getOpenId()));
            if (!StringUtils.hasText(watchStr)) {
                return 0;
            }
            JSONObject jsonObject = JSONObject.parseObject(watchStr);
            if (jsonObject != null && jsonObject.containsKey("totalDuration")) {
                int dailyWatchDuration = jsonObject.getIntValue("totalDuration");
                if (dailyWatchDuration >= watchDuration) {
                    return watchFactor;
                }
            }
        }
        return 0;
    }

    /**
     * 获取用户今日可抽奖总数
     */
    private int getDailyMaxLotteryCount(LotteryActivity lotteryActivity, Long userId) {
        String dailyShareKey = this.getDailyShareKey(lotteryActivity.getId(), userId);
        // 获取用户今日分享次数
        int dailyShareCount = this.getDailyShareCount(dailyShareKey);

        // 获取活动的每日抽奖最大次数配置
        int lotteryCount = lotteryActivity.getLotteryAmount();

        String activityInviteKey = this.getActivityInviteKey(lotteryActivity.getId());
        // 获取用户邀请成功-奖励抽奖次数
        int dailyInviteCount = this.getDailyInviteCount(activityInviteKey);

        if (Objects.nonNull(lotteryActivity.getInviteMaxAmount()) && lotteryActivity.getInviteMaxAmount() > 0) {
            if (lotteryActivity.getInviteMaxAmount() < dailyInviteCount) {
                dailyInviteCount = lotteryActivity.getInviteMaxAmount();
            }
        }

        int inviteFactor = lotteryActivity.getInviteFactor();


        int watchRewardCount = this.getWatchRewardCount(lotteryActivity);
        return lotteryCount + dailyShareCount + dailyInviteCount * inviteFactor + watchRewardCount;
    }

    /**
     * 获取用户今日已抽奖数
     */
    private int getDailyLotteryCount(Long activityId, Long userId, String activityDate) {
        LambdaQueryWrapper<LotteryLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(activityId), LotteryLog::getActivityId, activityId);
        queryWrapper.eq(StringUtils.hasText(activityDate), LotteryLog::getIssueDate, activityDate);
        queryWrapper.eq(Objects.nonNull(userId), LotteryLog::getUserId, userId);
        return (int) this.lotteryLogService.count(queryWrapper);
    }

    /**
     * 获取用户抽奖记录
     */
    private List<LotteryLog> getLotteryLogList(Long activityId, Long userId, String issueDate, Integer itemType, String deviceId) {
        LambdaQueryWrapper<LotteryLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(activityId), LotteryLog::getActivityId, activityId);
        queryWrapper.eq(StringUtils.hasText(issueDate), LotteryLog::getIssueDate, issueDate);
        queryWrapper.eq(Objects.nonNull(itemType), LotteryLog::getItemType, itemType);
        queryWrapper.eq(Objects.nonNull(userId), LotteryLog::getUserId, userId);
        queryWrapper.eq(StringUtils.hasText(deviceId), LotteryLog::getDeviceId, deviceId);
        queryWrapper.orderByDesc(LotteryLog::getId);
        IPage<LotteryLog> page = this.lotteryLogService.page(new Page<>(1, 10), queryWrapper);
        return page.getRecords();
    }

    /**
     * 获取用户抽奖数量
     */
    private int getLotteryLogCount(Long activityId, Long userId, String issueDate, List<Long> itemIdList, String deviceId) {
        LambdaQueryWrapper<LotteryLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Objects.nonNull(activityId), LotteryLog::getActivityId, activityId);
        queryWrapper.eq(StringUtils.hasText(issueDate), LotteryLog::getIssueDate, issueDate);
        queryWrapper.eq(Objects.nonNull(userId), LotteryLog::getUserId, userId);
        queryWrapper.in(!CollectionUtils.isEmpty(itemIdList), LotteryLog::getItemId, itemIdList);
        queryWrapper.eq(StringUtils.hasText(deviceId), LotteryLog::getDeviceId, deviceId);
        return (int) this.lotteryLogService.count(queryWrapper);
    }

    /**
     *
     */
    /**
     * 检查抽奖策略是否允许中奖
     *
     * @param lotteryPrizeDTO 奖品信息
     * @param activityId 活动ID
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @return 符合策略的奖品信息，不符合则返回null
     */
    private LotteryPrizeDTO checkLotteryStrategy(LotteryPrizeDTO lotteryPrizeDTO, Long activityId, Long userId, String deviceId) {
        if (Objects.isNull(lotteryPrizeDTO)) {
            return null;
        }

        // 获取中奖策略配置
        Integer userStrategy = lotteryPrizeDTO.getUserStrategy();
        Integer deviceStrategy = lotteryPrizeDTO.getDeviceStrategy();

        // 如果设备ID为空，则设备策略降级为不限制
        if ((Objects.equals(1, deviceStrategy) || Objects.equals(2, deviceStrategy)) && !StringUtils.hasText(deviceId)) {
            deviceStrategy = 0;
        }

        // 无限制策略直接通过
        if (Objects.equals(0, userStrategy) && Objects.equals(0, deviceStrategy)) {
            return lotteryPrizeDTO;
        }

        // 完全禁止策略直接拒绝
        if (Objects.equals(3, userStrategy) || Objects.equals(3, deviceStrategy)) {
            return null;
        }

        // 获取互斥奖项ID列表
        List<Long> mutuallyItemIds = getMutuallyItemIds(lotteryPrizeDTO);

        // 检查用户中奖限制
        if (Objects.equals(1, userStrategy) || Objects.equals(2, userStrategy)) {
            String dateFilter = Objects.equals(userStrategy, 1) ? LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null;

            int userLotteryCount = this.getLotteryLogCount(activityId, userId, dateFilter, mutuallyItemIds, null);
            if (userLotteryCount > 0) {
                return null;
            }
        }

        // 检查设备中奖限制
        if (Objects.equals(1, deviceStrategy) || Objects.equals(2, deviceStrategy)) {
            String dateFilter = Objects.equals(deviceStrategy, 1) ? LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) : null;

            int deviceLotteryCount = this.getLotteryLogCount(activityId, null, dateFilter, mutuallyItemIds, deviceId);
            if (deviceLotteryCount > 0) {
                return null;
            }
        }

        return lotteryPrizeDTO;
    }

    /**
     * 获取互斥奖项ID列表
     */
    private List<Long> getMutuallyItemIds(LotteryPrizeDTO lotteryPrizeDTO) {
        List<Long> mutuallyItemIds = new ArrayList<>();

        // 解析互斥奖项ID
        if (StringUtils.hasText(lotteryPrizeDTO.getMutuallyItemIds())) {
            try {
                mutuallyItemIds = Arrays.stream(lotteryPrizeDTO.getMutuallyItemIds().split(",")).map(String::trim).filter(StringUtils::hasText).map(Long::valueOf).collect(Collectors.toList());
            } catch (NumberFormatException e) {
                log.warn("解析互斥奖项ID失败: {}", lotteryPrizeDTO.getMutuallyItemIds(), e);
            }
        }

        // 添加当前奖项ID
        mutuallyItemIds.add(lotteryPrizeDTO.getItemId());
        return mutuallyItemIds;
    }

    /**
     * 获取活动信息
     */
    private LotteryActivity getActivityById(Long activityId) {
        LotteryActivity lotteryActivity = lotteryActivityService.getById(activityId);
        if (Objects.isNull(lotteryActivity) || !Objects.equals(1, lotteryActivity.getStatus()) || Objects.equals(1, lotteryActivity.getDelFlag())) {
            throw new ServiceException(-1, "活动不存在或已下架");
        }

        if (System.currentTimeMillis() >= lotteryActivity.getEndDate().toInstant(ZoneOffset.of("+8")).toEpochMilli()) {
            throw new ServiceException(-1, "活动已结束");
        }
        return lotteryActivity;
    }

    /**
     * 获取活动奖品配置信息
     */
    private LotteryItemConfig getItemConfig(Long activityId, Long itemId) {
        LambdaQueryWrapper<LotteryItemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LotteryItemConfig::getActivityId, activityId);
        queryWrapper.eq(LotteryItemConfig::getItemId, itemId);
        LotteryItemConfig config = this.lotteryItemConfigService.getOne(queryWrapper);
        if (Objects.isNull(config)) {
            throw new ServiceException(-1, "获取奖品配置失败");
        }
        return config;
    }

    /**
     * 获取奖品列表
     */
    private List<LotteryPrizeDTO> getLotteryPrizeList(Long activityId, String activityDate) {

        List<LotteryPrizeDTO> list = lotteryPrizeService.queryPrizeList(activityId, activityDate, true);
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException(-1, "奖品列表为空");
        }
        return list;
    }

    private BigDecimal randomBetween(BigDecimal min, BigDecimal max) {
        // 计算两个BigDecimal之间的差值
        BigDecimal range = max.subtract(min);
        Random random = new Random();
        // 生成0到1之间的随机小数
        BigDecimal randomFraction = BigDecimal.valueOf(random.nextDouble());
        // 将随机小数乘以差值
        BigDecimal randomValue = range.multiply(randomFraction);
        // 将随机值加到最小值上
        return min.add(randomValue).setScale(2, RoundingMode.DOWN);
    }

    public String getActivityInviteKey(Long activityId) {
        return String.format(JspRedisConstants.ACTIVITY_LOTTERY_INVITE_KEY, activityId, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), SecurityUtils.getOpenId());
    }
}
