package com.ruoyi.raffle.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class RaffleUser {
    /**
     * 抽奖用户ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 系统内部ID
     */
    private Long userId;
    /**
     * 对外用户ID，即今视频和赣云传过来的ID
     */
    private String openId;
    /**
     * 用户昵称
     */
    private String nickname;
    /**
     * 用户头像
     */
    private String avatar;
    /**
     * 手机号
     */
    private String mobile;

    /**
     * 是否订阅新活动通知
     */
    private Boolean isSubscribeNewActivityNotification;
    /**
     * 用户状态：0-禁用，1-启用
     */
    private Integer status;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人id
     */
    private Long updateId;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 逻辑删除标记
     */
    private Boolean isDeleted;

}
