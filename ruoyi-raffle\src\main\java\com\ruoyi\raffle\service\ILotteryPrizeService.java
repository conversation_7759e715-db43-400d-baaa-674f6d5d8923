package com.ruoyi.raffle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.raffle.domain.LotteryPrize;
import com.ruoyi.raffle.domain.dto.LotteryPrizeDTO;

import java.util.List;

/**
 * 抽奖奖品表 服务接口
 * 
 * <AUTHOR>
 */
public interface ILotteryPrizeService extends IService<LotteryPrize> {

    /**
     * 查询奖品列表
     *
     * @param activityId 活动ID
     * @param activityDate 活动日期
     * @param isLimitCurrentTime 是否限制当前时间
     * @return 奖品列表
     */
    List<LotteryPrizeDTO> queryPrizeList(Long activityId,String activityDate,Boolean isLimitCurrentTime);


    /**
     * 查询奖品池
     *
     * @param activityId 活动ID
     * @param activityDate 活动日期
     * @return 奖品列表
     */
    List<LotteryPrizeDTO> queryPrizePools(Long activityId,String activityDate);
}