package com.ruoyi.third.party.service.impl;

import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GeneratePresignedUrlRequest;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.ruoyi.third.party.domain.OssFile;
import com.ruoyi.third.party.domain.dto.request.FileUploadRequest;
import com.ruoyi.third.party.domain.dto.response.FileUploadResponse;
import com.ruoyi.third.party.mapper.OssFileMapper;
import com.ruoyi.third.party.service.IOssFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.URL;
import java.util.Date;
import java.util.Objects;
import java.util.UUID;

import static com.aliyun.oss.internal.OSSConstants.DEFAULT_OBJECT_CONTENT_TYPE;

@Slf4j
@Service
public class OssFileServiceImpl extends ServiceImpl<OssFileMapper, OssFile> implements IOssFileService {

    @Value("${aliyun.oss.region}")
    private String endpoint;

    @Value("${aliyun.oss.AccessKeyID}")
    private String accessKeyId;

    @Value("${aliyun.oss.AccessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucket}")
    private String bucket;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileUploadResponse create(FileUploadRequest request) {
        String ossId = generateId();
        String objName = request.getExtraId() + "/" + ossId;
        String preSignedUrl = generatePreSignedUrl(objName);

        OssFile entity = new OssFile();
        entity.setId(ossId);
        entity.setType(request.getType());
        entity.setName(request.getExtraId() + "/" + ossId);
        entity.setBucket(bucket);
        entity.setHeight(request.getHeight());
        entity.setWidth(request.getWidth());
        entity.setSize(request.getSize());
        entity.setUpload(false);

        this.save(entity);

        FileUploadResponse response = new FileUploadResponse();
        response.setId(ossId);
        response.setUrl(preSignedUrl);

        return response;
    }

    @Override
    public String getOriUrl(String id) {
        OssFile entity = this.getById(id);
        if (Objects.isNull(entity)) {
            return "";
        }


        // 设置缓存过期时间为一天
        Date expireDate = new Date(System.currentTimeMillis() + 224 * 60 * 60 * 1000);

        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        GeneratePresignedUrlRequest req = new GeneratePresignedUrlRequest(entity.getBucket(), entity.getName(), HttpMethod.GET);
        req.setExpiration(expireDate);
        URL signedUrl;
        try {
            signedUrl = ossClient.generatePresignedUrl(req);
        } finally {
            ossClient.shutdown();
        }
        return signedUrl.toString();
    }


    private String generatePreSignedUrl(String objectName) {
        // 创建OSSClient实例
        OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        try {
            GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucket, objectName, HttpMethod.PUT);
            // 设置URL过期时间为1小时
            request.setExpiration(new Date(System.currentTimeMillis() + 60 * 60 * 1000));
            // 设置ContentType
            request.setContentType(DEFAULT_OBJECT_CONTENT_TYPE);
            // 生成PUT方式的签名URL
            URL signedUrl = ossClient.generatePresignedUrl(request);
            return signedUrl.toString();
        } finally {
            // 关闭OSSClient
            ossClient.shutdown();
        }
    }


    private String generateId() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }
}
