
package com.ruoyi.app.model.lottery;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 用户抽奖活动结果
 * 
 * <AUTHOR>
 * @date 2024/1/30 11:04
 */
@Data
@ApiModel
public class LotteryActivityInfoVO implements Serializable {


    @ApiModelProperty("开始时间")
    private Long startTime;

    @ApiModelProperty("结束时间")
    private Long endTime;

    @ApiModelProperty("是否是最后一天")
    private Boolean isEndDay;

    @ApiModelProperty("可抽奖次数")
    private Integer lotteryAmount;

    @ApiModelProperty("可分享次数")
    private Integer shareAmount;

    @ApiModelProperty("抽中物品信息")
    private List<GoodsInfo> goodsList;

    @ApiModelProperty("奖品列表")
    private List<PrizeDetail> prizeList;

    @ApiModelProperty("今日邀请成功数")
    private Integer inviteCount;

    @ApiModelProperty("今日观看任务获得数")
    private Integer watchCount;

    @ApiModelProperty("当前用户是否开启青少年模式")
    private Boolean isJuniorMode;

    @Data
    public static class GoodsInfo {
        @ApiModelProperty("抽奖日志ID")
        private Long logId;

        @ApiModelProperty("奖品名称")
        private String name;

        @ApiModelProperty("奖品图片")
        private String coverUrl;

        @ApiModelProperty("抽中时间")
        @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
        private Date issueTime;

        @ApiModelProperty("物品券码（如果有）")
        private String couponCode;

        @ApiModelProperty("绑定手机号（抽中物品时当需要绑定手机号）")
        private String bindMobile;

        @ApiModelProperty("绑定姓名（抽中物品时当需要绑定姓名）")
        private String bindName;

        @ApiModelProperty("type=3为物品时,是否需要绑定手机号 0-不需要 1-需要 2-使用当前账户的手机号")
        private Integer bindMobileFlag;

        @ApiModelProperty("type=3为物品时,是否需要绑定姓名 0-不需要 1-需要")
        private Integer bindNameFlag;
    }

    @Data
    public static class PrizeDetail {
        @ApiModelProperty("奖品ID")
        private Long id;

        @ApiModelProperty("奖项ID(用于匹配转盘指针指向)")
        private Long itemId;

        @ApiModelProperty("类型: 1-现金 2-今豆 3-物品")
        private Integer type;

        @ApiModelProperty("奖品名称")
        private String name;

        @ApiModelProperty("奖品图片")
        private String coverUrl;

        @ApiModelProperty("奖品序号")
        private Integer sort;
    }
}
