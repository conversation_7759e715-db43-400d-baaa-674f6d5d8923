package com.ruoyi.raffle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.raffle.domain.LotteryItemConfig;
import com.ruoyi.raffle.domain.dto.LotteryItemConfigInfoDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 抽奖奖品配置表数据访问层
 * 提供奖品配置相关的数据库操作方法
 * 
 * <AUTHOR>
 * @date 2023-12-19
 */
public interface LotteryItemConfigMapper extends BaseMapper<LotteryItemConfig> {

    /**
     * 查询奖品配置信息列表（包含奖项基本信息）
     *
     * @param activityId 活动ID
     * @return 奖品配置信息列表
     */
    List<LotteryItemConfigInfoDTO> queryItemConfigInfoList(@Param("activityId") Long activityId);
}
