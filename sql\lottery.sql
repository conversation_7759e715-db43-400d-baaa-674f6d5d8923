/*
 Navicat Premium Data Transfer

 Source Server         : mysql.dev-base
 Source Server Type    : MySQL
 Source Server Version : 100419
 Source Host           : *************:30306
 Source Schema         : jsp_raffle

 Target Server Type    : MySQL
 Target Server Version : 100419
 File Encoding         : 65001

 Date: 19/06/2025 15:18:35
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for lottery_prize_time_period
-- ----------------------------
DROP TABLE IF EXISTS `lottery_prize_time_period`;
CREATE TABLE `lottery_prize_time_period`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `prize_id` bigint(20) NULL DEFAULT NULL COMMENT '奖品ID',
  `start_time` time(0) NULL DEFAULT '00:00:00' COMMENT '奖品开放开始时间',
  `end_time` time(0) NULL DEFAULT '23:59:59' COMMENT '奖品开放结束时间',
  `amount` int(10) NULL DEFAULT 0 COMMENT '数量',
  `issue_amount` int(10) NULL DEFAULT 0 COMMENT '已发放数量',
  `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
  `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建用户账号',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
  `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新用户账号',
  `update_date` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `lottery_prize_time_period_idx_2`(`prize_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '抽奖奖品-中奖时间控制表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for lottery_prize_template_time_period
-- ----------------------------
DROP TABLE IF EXISTS `lottery_prize_template_time_period`;
CREATE TABLE `lottery_prize_template_time_period`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `template_id` bigint(20) NULL DEFAULT NULL COMMENT '模板ID',
  `start_time` time(0) NULL DEFAULT '00:00:00' COMMENT '奖品开放开始时间',
  `end_time` time(0) NULL DEFAULT '23:59:59' COMMENT '奖品开放结束时间',
  `amount` int(10) NULL DEFAULT 0 COMMENT '数量',
  `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
  `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建用户账号',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
  `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新用户账号',
  `update_date` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `template_time_period_idx_1`(`template_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '抽奖奖品模板-中奖时间控制表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for lottery_prize_template
-- ----------------------------
DROP TABLE IF EXISTS `lottery_prize_template`;
CREATE TABLE `lottery_prize_template`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动id',
  `item_id` bigint(20) NULL DEFAULT NULL COMMENT '奖项id',
  `amount` int(10) NULL DEFAULT 0 COMMENT '总数量',
  `prize_type` tinyint(4) NULL DEFAULT 0 COMMENT '奖品类型 0-优先 1-保底',
  `user_strategy` tinyint(4) NULL DEFAULT 0 COMMENT '用户中奖策略：0-不限制  1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中',
  `device_strategy` tinyint(4) NULL DEFAULT 0 COMMENT '设备中奖策略：0-不限制  1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中',
  `mutually_item_ids` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '互斥的奖项id集合',
  `sort` int(10) UNSIGNED NULL DEFAULT 1 COMMENT '顺序',
  `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
  `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建用户账号',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
  `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新用户账号',
  `update_date` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `lottery_prize_idx_2`(`activity_id`) USING BTREE,
  INDEX `lottery_prize_idx_1`(`item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '抽奖奖品模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for lottery_prize
-- ----------------------------
DROP TABLE IF EXISTS `lottery_prize`;
CREATE TABLE `lottery_prize`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动id',
  `activity_date` date NULL DEFAULT NULL COMMENT '活动日期',
  `template_id` bigint(20) NULL DEFAULT NULL COMMENT '模板id',
  `item_id` bigint(20) NULL DEFAULT NULL COMMENT '奖项id',
  `amount` int(10) NULL DEFAULT 0 COMMENT '总数量',
  `issue_amount` int(10) NULL DEFAULT 0 COMMENT '已发放数量',
  `prize_type` tinyint(4) NULL DEFAULT 0 COMMENT '奖品类型 0-优先 1-保底',
  `user_strategy` tinyint(4) NULL DEFAULT 0 COMMENT '用户中奖策略：0-不限制  1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中',
  `device_strategy` tinyint(4) NULL DEFAULT 0 COMMENT '设备中奖策略：0-不限制  1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中',
  `mutually_item_ids` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '互斥的奖项id集合',
  `sort` int(10) UNSIGNED NULL DEFAULT 1 COMMENT '顺序',
  `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
  `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建用户账号',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
  `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新用户账号',
  `update_date` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `lottery_prize_idx_2`(`activity_id`) USING BTREE,
  INDEX `lottery_prize_idx_1`(`item_id`) USING BTREE,
  INDEX `lottery_prize_idx_3`(`activity_date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '抽奖奖品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for lottery_log
-- ----------------------------
DROP TABLE IF EXISTS `lottery_log`;
CREATE TABLE `lottery_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动id',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `device_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '设备ID',
  `ip_addr` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'ip地址',
  `ip_location` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'ip地点',
  `item_id` bigint(20) NULL DEFAULT NULL COMMENT '奖项id',
  `item_type` tinyint(4) NULL DEFAULT NULL COMMENT '奖项类型: 1-现金 2-今豆 3-物品',
  `item_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '奖项名称',
  `item_val` decimal(10, 2) NULL DEFAULT NULL COMMENT '奖项值',
  `prize_id` bigint(20) NULL DEFAULT NULL COMMENT '奖品ID',
  `prize_type` tinyint(4) NULL DEFAULT 0 COMMENT '奖品类型 0-优先 1-保底',
  `issue_date` date NULL DEFAULT NULL COMMENT '发放日期',
  `coupon_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '券码',
  `bind_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '绑定的姓名',
  `bind_mobile` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '绑定的手机号',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '备注',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_date` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `lottery_log_idx_1`(`activity_id`) USING BTREE,
  INDEX `lottery_log_idx_3`(`issue_date`) USING BTREE,
  INDEX `lottery_log_idx_4`(`device_id`) USING BTREE,
  INDEX `lottery_log_idx_5`(`user_id`) USING BTREE,
  INDEX `lottery_log_idx_2`(`item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '抽奖日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for lottery_item_config
-- ----------------------------
DROP TABLE IF EXISTS `lottery_item_config`;
CREATE TABLE `lottery_item_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动id',
  `item_id` bigint(20) NOT NULL COMMENT '奖项id',
  `bind_mobile_flag` tinyint(4) NULL DEFAULT 0 COMMENT '是否需要绑定手机号 0-不需要 1-需要 2-使用当前账户的手机号',
  `bind_name_flag` tinyint(4) NULL DEFAULT 0 COMMENT '是否需要绑定姓名 0-不需要 1-需要',
  `mobile_unique_flag` tinyint(4) NULL DEFAULT 0 COMMENT '当需要绑定手机号时，手机号是否唯一： 0-不唯一 1-唯一',
  `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
  `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建用户账号',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
  `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新用户账号',
  `update_date` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `lottery_item_config_idx_1`(`activity_id`, `item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '抽奖奖品配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for lottery_item_code
-- ----------------------------
DROP TABLE IF EXISTS `lottery_item_code`;
CREATE TABLE `lottery_item_code`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `activity_id` bigint(20) NOT NULL COMMENT '活动id',
  `item_id` bigint(20) NOT NULL COMMENT '奖项id',
  `code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '券码',
  `grant_type` tinyint(4) NULL DEFAULT 0 COMMENT '发放标识 0-未发放 1-已发放',
  `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
  `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建用户账号',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
  `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新用户账号',
  `update_date` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `lottery_item_code_idx_1`(`activity_id`, `item_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '抽奖奖项-券码表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for lottery_item
-- ----------------------------
DROP TABLE IF EXISTS `lottery_item`;
CREATE TABLE `lottery_item`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` tinyint(2) NULL DEFAULT NULL COMMENT '类型: 1-现金 2-今豆 3-物品',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '奖项名称',
  `cover` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '奖项图片',
  `prize_min` decimal(10, 2) NULL DEFAULT NULL COMMENT '奖品最小值',
  `prize_max` decimal(10, 2) NULL DEFAULT NULL COMMENT '奖品最大值',
  `code_grant_type` tinyint(2) NULL DEFAULT 0 COMMENT '券码发放类型: 0-不发放 1-发放',
  `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
  `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建用户账号',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
  `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新用户账号',
  `update_date` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '抽奖奖项表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for lottery_activity
-- ----------------------------
DROP TABLE IF EXISTS `lottery_activity`;
CREATE TABLE `lottery_activity`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '活动id',
  `title` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '活动主题',
  `start_date` datetime(0) NULL DEFAULT NULL COMMENT '活动开始时间',
  `end_date` datetime(0) NULL DEFAULT NULL COMMENT '活动结束时间',
  `introduction` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '活动简介',
  `status` tinyint(1) NULL DEFAULT 0 COMMENT '活动状态：0-未上架、1-上架中、2-已下架',
  `type` tinyint(255) NULL DEFAULT 1 COMMENT '活动类型 1-按库存抽奖 2-按概率抽奖',
  `del_flag` tinyint(1) NULL DEFAULT 0 COMMENT '删除标志  0-未删除 1-已删除',
  `weight` tinyint(4) NULL DEFAULT 0 COMMENT '比重',
  `lottery_amount` int(11) NULL DEFAULT 1 COMMENT '每日能抽奖次数(或者每日登录奖励次数)',
  `share_max_amount` int(11) NULL DEFAULT 1 COMMENT '每日分享获得最大次数',
  `deplete_bean_type` tinyint(4) NULL DEFAULT 0 COMMENT '抽奖消耗今豆配置 0-不消耗 1-每次都消耗 2-每天第一次消耗',
  `deplete_bean` int(11) NULL DEFAULT 0 COMMENT '抽奖消耗今豆数',
  `device_limit` int(11) NULL DEFAULT 5 COMMENT '限制设备正常抽奖次数（每日）',
  `watch_duration` int(11) NULL DEFAULT 30 COMMENT '配置观看时长奖励抽奖次数（观看时长，秒）',
  `watch_factor` int(11) NULL DEFAULT 1 COMMENT '观看时长达标获取抽奖次数 系数',
  `invite_factor` int(11) NULL DEFAULT 2 COMMENT '邀请成功获取抽奖次数 系数',
  `invite_max_amount` int(11) NULL DEFAULT 20 COMMENT '邀请成功人数奖励上限',
  `invite_reward_bean` int(11) NULL DEFAULT 400 COMMENT '邀请成功奖励今豆数',
  `backstop_bean_min` int(11) NULL DEFAULT 6 COMMENT '兜底今豆数-最小值',
  `backstop_bean_max` int(11) NULL DEFAULT 6 COMMENT '兜底今豆数-最大值',
  `limit_ip_addr` tinyint(4) NULL DEFAULT 0 COMMENT '限制ip抽奖 0-不限制 1-限制',
  `create_user_id` bigint(20) NULL DEFAULT NULL COMMENT '创建用户id',
  `create_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建用户账号',
  `create_date` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `update_user_id` bigint(20) NULL DEFAULT NULL COMMENT '更新用户id',
  `update_user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '更新用户账号',
  `update_date` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '抽奖活动表' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
