<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.raffle.mapper.LotteryPrizeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.raffle.domain.LotteryPrize">
        <id column="id" property="id" />
        <result column="activity_id" property="activityId" />
        <result column="activity_date" property="activityDate" />
        <result column="template_id" property="templateId" />
        <result column="item_id" property="itemId" />
        <result column="prize_type" property="prizeType" />
        <result column="user_strategy" property="userStrategy" />
        <result column="device_strategy" property="deviceStrategy" />
        <result column="mutually_item_ids" property="mutuallyItemIds" />
        <result column="sort" property="sort" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_date" property="createDate" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_date" property="updateDate" />
    </resultMap>

    <select id="queryPrizeList" resultType="com.ruoyi.raffle.domain.dto.LotteryPrizeDTO">

        SELECT
            t1.id,
            t1.prize_type prizeType,
            tt.id periodId,
            tt.amount,
            tt.issue_amount issueAmount,
            tt.start_time startTime,
            tt.end_time endTime,
            t1.sort,
            t1.user_strategy userStrategy,
            t1.device_strategy deviceStrategy,
            t1.mutually_item_ids mutuallyItemIds,
            t2.id itemId,
            t2.type,
            t2.`name`,
            t2.cover,
            t2.prize_min prizeMin,
            t2.prize_max prizeMax,
            t2.code_grant_type codeGrantType
        FROM
            lottery_prize_time_period tt left join lottery_prize t1 on tt.prize_id = t1.id
             left join  lottery_item t2 on t1.item_id = t2.id
        WHERE
            t1.activity_id = #{activityId}

            <if test="activityDate != null and activityDate != ''">
                and t1.activity_date = #{activityDate}
            </if>

            <if test="isLimitCurrentTime != null and isLimitCurrentTime == true">
                and tt.start_time &lt;= now() and tt.end_time &gt;= now()
            </if>

        order by t1.prize_type,t1.sort asc

    </select>
    <select id="queryPrizePools" resultType="com.ruoyi.raffle.domain.dto.LotteryPrizeDTO">
        SELECT
        t1.id,
        t1.sort,
        t1.prize_type prizeType,
        t1.user_strategy userStrategy,
        t1.device_strategy deviceStrategy,
        t1.mutually_item_ids mutuallyItemIds,
        t2.id itemId,
        t2.type,
        t2.`name`,
        t2.cover,
        t2.code_grant_type codeGrantType
        FROM
        lottery_prize t1 left join  lottery_item t2 on t1.item_id = t2.id
        WHERE
        t1.activity_id = #{activityId}

        <if test="activityDate != null and activityDate != ''">
            and t1.activity_date = #{activityDate}
        </if>
        order by t1.prize_type,t1.sort asc
    </select>

</mapper>