package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.raffle.domain.LotteryPrizeTemplate;
import com.ruoyi.raffle.domain.dto.LotteryTemplateInfoDTO;
import com.ruoyi.raffle.mapper.LotteryPrizeTemplateMapper;
import com.ruoyi.raffle.service.ILotteryPrizeTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 抽奖奖品模板表业务逻辑实现类
 * 实现奖品模板相关的业务操作
 * 
 * <AUTHOR>
 * @date 2023-12-18
 */
@Slf4j
@Service
public class LotteryPrizeTemplateServiceImpl extends ServiceImpl<LotteryPrizeTemplateMapper, LotteryPrizeTemplate> 
        implements ILotteryPrizeTemplateService {


    @Override
    public List<LotteryTemplateInfoDTO> queryTemplateInfoList(Long activityId) {
        return this.baseMapper.queryTemplateInfoList(activityId);
    }
}
