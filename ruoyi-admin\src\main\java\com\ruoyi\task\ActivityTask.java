package com.ruoyi.task;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.event.RaffleNotificationEvent;
import com.ruoyi.raffle.domain.Activity;
import com.ruoyi.raffle.domain.ActivityMember;
import com.ruoyi.raffle.service.IActivityMemberService;
import com.ruoyi.raffle.service.IActivityService;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Component("activityTask")
public class ActivityTask {

    @Resource
    private IActivityService activityService;
    @Resource
    private IActivityMemberService activityMemberService;
    @Resource
    private ApplicationEventPublisher applicationEventPublisher;


    /**
     * 定时扫描抽奖任务
     */
    public void startRaffle() {
        System.out.println("定时扫描抽奖任务");
        LambdaQueryWrapper<Activity> activityQuery = Wrappers.lambdaQuery();
        activityQuery.eq(Activity::getRaffleStatus, 0);
        activityQuery.eq(Activity::getStatus, 1);
        activityQuery.lt(Activity::getRaffleOpenTime, new Date());

        //  待开奖的活动列表
        List<Activity> activities = activityService.list(activityQuery);

        for (Activity activity : activities) {
            //  奖励列表
            List<ActivityMember> prizeList = new ArrayList<>();
            //  未抽奖人员查询
            LambdaQueryWrapper<ActivityMember> unRaffledQuery = Wrappers.lambdaQuery();
            unRaffledQuery.eq(ActivityMember::getActivityId, activity.getId());
            unRaffledQuery.eq(ActivityMember::getRaffleStatus, 0);
            List<ActivityMember> unRaffledMembers = activityMemberService.list(unRaffledQuery);

            //  已经中奖的人数（内部安排，提前DB设置中奖状态）
            LambdaQueryWrapper<ActivityMember> raffledQuery = Wrappers.lambdaQuery();
            raffledQuery.eq(ActivityMember::getActivityId, activity.getId());
            raffledQuery.eq(ActivityMember::getRaffleStatus, 1);
            List<ActivityMember> raffledMembers = activityMemberService.list(raffledQuery);


            //  打乱列表顺序
            Collections.shuffle(unRaffledMembers);
            //  抽取前n个
            int limit = Optional.ofNullable(activity.getActivityPrizeNum()).filter(e -> e > 0).orElse(0);

            if (raffledMembers.size() >= limit) {

                prizeList.addAll(raffledMembers.stream().limit(limit).collect(Collectors.toList()));

            }else {
                //  内部安排人员
                prizeList.addAll(raffledMembers);

                //  抽取人员
                prizeList.addAll(unRaffledMembers.stream().limit(limit - raffledMembers.size()).collect(Collectors.toList()));
            }


            //  构建中奖用户信息
            for (ActivityMember activityApply : prizeList) {
                //  标记为已中奖
                activityApply.setWinTime(activity.getRaffleOpenTime());
                activityApply.setRaffleStatus(1);
                activityApply.setExchangeStatus(0);

                activityMemberService.updateById(activityApply);

                //  发送通知事件
                applicationEventPublisher.publishEvent(new RaffleNotificationEvent(activityApply.getId()));
            }

            //  更新活动开奖状态
            activity.setRaffleStatus(1);
            activity.setStatus(2);
            activityService.updateById(activity);

        }

    }

}
