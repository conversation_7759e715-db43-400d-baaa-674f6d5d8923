package com.ruoyi.system.domain;

import java.io.Serializable;

/**
 * 租户表 sys_tenant
 *
 * <AUTHOR>
 */
public class SysTenant implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 租户ID
     */
    private String id;
    /**
     * 租户名称
     */

    private String name;
    /**
     * 租户所属平台：1-今视频，2-赣云
     */
    private Integer platform;

    /**
     * 状态（0-停用， 1-启用）
     */

    private String status;


    public SysTenant(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "SysTenant{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", platform=" + platform +
                ", status='" + status + '\'' +
                '}';
    }
}
