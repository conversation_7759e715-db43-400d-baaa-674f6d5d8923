package com.ruoyi.web.controller.lottery;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.raffle.domain.LotteryItemCode;
import com.ruoyi.raffle.domain.LotteryItemConfig;
import com.ruoyi.raffle.domain.dto.LotteryItemConfigInfoDTO;
import com.ruoyi.raffle.service.ILotteryActivityService;
import com.ruoyi.raffle.service.ILotteryItemCodeService;
import com.ruoyi.raffle.service.ILotteryItemConfigService;
import com.ruoyi.raffle.service.ILotteryPrizeService;
import com.ruoyi.third.party.service.IOssFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 抽奖奖项表 控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/lottery/item/config")
@Api("物品配置管理")
public class LotteryItemConfigController extends BaseController {

    @Resource
    private ILotteryItemConfigService lotteryItemConfigService;

    @Resource
    private ILotteryItemCodeService lotteryItemCodeService;

    @Resource
    private ILotteryActivityService lotteryActivityService;

    @Resource
    private IOssFileService ossFileService;

    @GetMapping("/list")
    @ApiOperation("查询物品配置列表")
    public TableDataInfo list(LotteryItemConfig lotteryItemConfig) {
        startPage();
        List<LotteryItemConfigInfoDTO> list = lotteryItemConfigService.queryItemConfigInfoList(lotteryItemConfig.getActivityId());
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(e -> {
                e.setCoverUrl(ossFileService.getOriUrl(e.getCover()));
                e.setCodeAmount(this.lotteryItemCodeService.count(new LambdaQueryWrapper<LotteryItemCode>().eq(LotteryItemCode::getActivityId, lotteryItemConfig.getActivityId()).eq(LotteryItemCode::getItemId, e.getItemId())));
                e.setCodeGrantAmount(this.lotteryItemCodeService.count(new LambdaQueryWrapper<LotteryItemCode>().eq(LotteryItemCode::getActivityId, lotteryItemConfig.getActivityId()).eq(LotteryItemCode::getItemId, e.getItemId()).eq(LotteryItemCode::getGrantType, 1)));
                e.setPrizeAmount(lotteryActivityService.calculateTotalStock(lotteryItemConfig.getActivityId(), e.getItemId()));
            });
        }
        return getDataTable(list);
    }

    /**
     * 新增物品配置
     */
    @Log(title = "物品配置", businessType = BusinessType.UPDATE)
    @PutMapping()
    @ApiOperation("批量修改物品配置")
    public AjaxResult batchUpdate(@RequestBody List<LotteryItemConfig> configList) {
        if (CollectionUtils.isEmpty(configList)) {
            return success(true);
        }
        configList.forEach(e -> {
            e.setUpdateUserId(getLoginUser().getUserId());
            e.setUpdateUserName(getLoginUser().getUsername());
            e.setUpdateDate(DateUtils.getNowDate());
        });


        return toAjax(lotteryItemConfigService.updateBatchById(configList));
    }


    /**
     * 查询抽奖奖项列表
     */
    // //@PreAuthorize("@ss.hasPermi('lottery:item:list')")
    @GetMapping("/code/list")
    @ApiOperation("查询物品券码配置列表")
    public TableDataInfo list(LotteryItemCode lotteryItemCode) {
        startPage();
        LambdaQueryWrapper<LotteryItemCode> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(lotteryItemCode.getCode())) {
            queryWrapper.like(LotteryItemCode::getCode, lotteryItemCode.getCode());
        }
        if (Objects.nonNull(lotteryItemCode.getGrantType())) {
            queryWrapper.eq(LotteryItemCode::getGrantType, lotteryItemCode.getGrantType());
        }
        queryWrapper.orderByDesc(LotteryItemCode::getId);
        List<LotteryItemCode> list = lotteryItemCodeService.list(queryWrapper);
        return getDataTable(list);
    }

    @PostMapping("/code/importTemplate")
    @ApiOperation("下载物品券码导入模板")
    public void importTemplate(HttpServletResponse response) {

        response.setHeader("Content-disposition", "attachment;filename=code.xlsx");
        ExcelUtil<LotteryItemCode> util = new ExcelUtil<LotteryItemCode>(LotteryItemCode.class);
        util.importTemplateExcel(response, "Sheet1");
    }

    @Log(title = "物品券码配置", businessType = BusinessType.IMPORT)
    @PostMapping("/code/importData")
    @ApiOperation("导入物品券码")
    public AjaxResult importData(MultipartFile file, Long activityId, Long itemId) throws Exception {
        ExcelUtil<LotteryItemCode> util = new ExcelUtil<>(LotteryItemCode.class);
        List<LotteryItemCode> list = util.importExcel(file.getInputStream());
        AtomicInteger successNum = new AtomicInteger();
        AtomicInteger ignoreNum = new AtomicInteger();

        list.forEach(e -> {
            if (StringUtils.isBlank(e.getCode())) {
                ignoreNum.getAndIncrement();
                return;
            }
            LotteryItemCode existCode = this.isExistCode(e.getCode(), activityId, itemId);
            if (Objects.nonNull(existCode)) {
                ignoreNum.getAndIncrement();
                return;
            }
            LotteryItemCode entity = new LotteryItemCode();
            entity.setActivityId(activityId);
            entity.setItemId(itemId);
            entity.setCode(e.getCode());
            entity.setGrantType(0);
            entity.setCreateUserId(getLoginUser().getUserId());
            entity.setCreateUserName(getLoginUser().getUsername());
            entity.setCreateDate(DateUtils.getNowDate());
            boolean save = lotteryItemCodeService.save(entity);
            if (save) {
                successNum.getAndIncrement();
            }
        });
        return success("导入成功，成功导入 " + successNum + " 条数据，忽略 " + ignoreNum + " 条数据!");
    }

    /**
     * 新增物品券码配置
     */
    @Log(title = "物品券码配置", businessType = BusinessType.INSERT)
    @PostMapping("/code")
    @ApiOperation("新增物品券码")
    public AjaxResult add(@RequestBody LotteryItemCode lotteryItemCode) {
        LotteryItemCode existCode = this.isExistCode(lotteryItemCode.getCode(), lotteryItemCode.getActivityId(), lotteryItemCode.getItemId());
        if (Objects.nonNull(existCode)) {
            return error("该券码已存在!");
        }
        lotteryItemCode.setCreateUserId(getLoginUser().getUserId());
        lotteryItemCode.setCreateUserName(getLoginUser().getUsername());
        lotteryItemCode.setCreateDate(DateUtils.getNowDate());
        return toAjax(lotteryItemCodeService.save(lotteryItemCode));
    }

    /**
     * 修改物品券码配置
     */
    @Log(title = "物品券码配置", businessType = BusinessType.UPDATE)
    @PutMapping("/code")
    @ApiOperation("修改物品券码")
    public AjaxResult edit(@RequestBody LotteryItemCode lotteryItemCode) {
        LotteryItemCode existCode = this.isExistCode(lotteryItemCode.getCode(), lotteryItemCode.getActivityId(), lotteryItemCode.getItemId());
        if (Objects.nonNull(existCode)) {
            return error("该券码已存在!");
        }
        lotteryItemCode.setUpdateUserId(getLoginUser().getUserId());
        lotteryItemCode.setUpdateUserName(getLoginUser().getUsername());
        lotteryItemCode.setUpdateDate(DateUtils.getNowDate());
        return toAjax(lotteryItemCodeService.updateById(lotteryItemCode));
    }

    /**
     * 删除物品券码配置
     */
    //// @PreAuthorize("@ss.hasPermi('lottery:item:remove')")
    @Log(title = "物品券码配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/code/{id}")
    @ApiOperation("删除物品券码")
    public AjaxResult removeCode(@PathVariable Long id) {
        LotteryItemCode entity = this.lotteryItemCodeService.getById(id);
        if (Objects.isNull(entity)) {
            return error("券码不存在!");
        }
        if (Objects.equals(1, entity.getGrantType())) {
            return error("券码已发放，不能删除!");
        }

        return toAjax(this.lotteryItemCodeService.removeById(id));
    }

    @Log(title = "物品券码配置", businessType = BusinessType.EXPORT)
    @PostMapping("/code/export")
    @ApiOperation("导出物品券码")
    public void export(HttpServletResponse response, Long activityId, Long itemId) {
        LambdaQueryWrapper<LotteryItemCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LotteryItemCode::getActivityId, activityId);
        queryWrapper.eq(LotteryItemCode::getItemId, itemId);
        List<LotteryItemCode> list = this.lotteryItemCodeService.list(queryWrapper);

        String fileName = "code_" + System.currentTimeMillis() + ".xlsx";
        response.setHeader("Content-disposition", "attachment;filename=" + fileName);
        ExcelUtil<LotteryItemCode> util = new ExcelUtil<>(LotteryItemCode.class);
        util.exportExcel(response, list, "操作日志");
    }

    LotteryItemCode isExistCode(String code, Long activityId, Long itemId) {
        LambdaQueryWrapper<LotteryItemCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LotteryItemCode::getActivityId, activityId);
        queryWrapper.eq(LotteryItemCode::getItemId, itemId);
        queryWrapper.eq(LotteryItemCode::getCode, code);
        return this.lotteryItemCodeService.getOne(queryWrapper);
    }

    LotteryItemConfig isExistItemConfig(Long activityId, Long itemId) {
        LambdaQueryWrapper<LotteryItemConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LotteryItemConfig::getActivityId, activityId);
        queryWrapper.eq(LotteryItemConfig::getItemId, itemId);
        return this.lotteryItemConfigService.getOne(queryWrapper);
    }
}
