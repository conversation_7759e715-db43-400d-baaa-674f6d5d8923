package com.ruoyi.raffle.domain.dto;

import java.math.BigDecimal;
import java.time.LocalTime;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/7 4:56 下午
 */

@Data
public class LotteryPrizeDTO {

    /**
     * 主键ID
     */
    private Long id;


    /**
     * 奖项id
     */
    private Long itemId;

    /**
     * 类型: 1-现金 2-今豆 3-物品
     */
    private Integer type;

    /**
     * 奖项名称
     */
    private String name;

    /**
     * 奖项图片
     */
    private String cover;

    /**
     * 奖品最小值
     */
    private BigDecimal prizeMin;

    /**
     * 奖品最大值
     */
    private BigDecimal prizeMax;

    /**
     * 券码发放类型: 0-不发放 1-发放
     */
    private Integer codeGrantType;

    /**
     * 奖品时间控制id
     */
    private Long periodId;

    /**
     * 总数量
     */
    private Integer amount;

    /**
     * 已发放数量
     */
    private Integer issueAmount;

    /**
     * 奖品类型 0-优先 1-保底
     */
    private Integer prizeType;

    /**
     * 顺序
     */
    private Integer sort;

    /**
     * 奖品开放开始时间
     */
    private LocalTime startTime;

    /**
     * 奖品开放结束时间
     */
    private LocalTime endTime;

    /**
     * 用户中奖策略：0-不限制 1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中
     */
    private Integer userStrategy;

    /**
     * 设备中奖策略：0-不限制 1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中
     */
    private Integer deviceStrategy;

    /**
     * 互斥的奖项id集合
     */
    private String mutuallyItemIds;

    /**
     * 概率
     */
    private Double probability;
}
