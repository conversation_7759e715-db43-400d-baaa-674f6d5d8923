package com.ruoyi.raffle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.raffle.domain.LotteryLog;
import com.ruoyi.third.party.domain.dto.request.jsp.JspLotteryOperateRequest;
import com.ruoyi.third.party.domain.dto.response.jsp.JspLotteryOperateResponse;

/**
 * 抽奖日志表 服务接口
 * 
 * <AUTHOR>
 */
public interface ILotteryLogService extends IService<LotteryLog> {

    Long saveLotteryInfo(LotteryLog log, Long prizeTimePeriodId,String title);

    JspLotteryOperateResponse lotteryOperateJsp(JspLotteryOperateRequest operateRequest);
}