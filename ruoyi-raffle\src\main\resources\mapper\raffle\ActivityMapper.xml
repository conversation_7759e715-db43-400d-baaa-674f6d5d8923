<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.raffle.mapper.ActivityMapper">
    <select id="queryActiveList" resultType="com.ruoyi.raffle.domain.Activity">
        SELECT id,
               activity_name,
               activity_rule,
               activity_description,
               activity_num,
               raffle_open_time,
               activity_promo_pic,
               bg_color,
               is_dark_font,
               activity_prize,
               activity_prize_num,
               activity_prize_value,
               activity_prize_pic,
               activity_url,
               activity_prize_need_sent,
               raffle_status,
               `status`,
               tenant_id,
               create_id,
               create_name,
               update_id,
               update_name,
               update_date,
               is_deleted
        FROM activity
        WHERE tenant_id = #{tenantId}
          AND `status` IN (1, 2)
        ORDER BY `status`, ABS(TIMEDIFF(now(), raffle_open_time))
    </select>
    <select id="queryPublicActivityList" resultType="com.ruoyi.raffle.domain.Activity">
        SELECT id,
               activity_name,
               activity_rule,
               activity_description,
               activity_num,
               raffle_open_time,
               activity_promo_pic,
               bg_color,
               is_dark_font,
               activity_prize,
               activity_prize_num,
               activity_prize_value,
               activity_prize_pic,
               activity_url,
               activity_prize_need_sent,
               raffle_status,
               `status`,
               tenant_id,
               create_id,
               create_name,
               update_id,
               update_name,
               update_date,
               is_deleted
        FROM activity
        WHERE tenant_id = #{tenantId}
          AND `status` = 1  and is_deleted =0
        ORDER BY  id desc


    </select>
</mapper>