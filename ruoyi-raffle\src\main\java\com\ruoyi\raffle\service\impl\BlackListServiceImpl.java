package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.raffle.domain.BlackList;
import com.ruoyi.raffle.mapper.BlackListMapper;
import com.ruoyi.raffle.service.IBlackListService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 黑名单业务逻辑实现类
 * 实现黑名单相关的业务操作
 * 
 * <AUTHOR>
 * @date 2023-12-18
 */
@Service
public class BlackListServiceImpl extends ServiceImpl<BlackListMapper, BlackList> implements IBlackListService {
    @Override
    public boolean addToBlackList(String openId, String reason) {
        // 参数校验
        if (!StringUtils.hasText(openId)) {
            return false;
        }

        // 获取当前登录用户信息
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();
        Date now = new Date();

        // 由于openId有唯一索引，需要先查询是否已存在记录
        BlackList existingRecord = this.getByOpenId(openId);

        if (existingRecord != null) {
            // 如果记录存在且未删除，直接返回true（已在黑名单中）
            if (existingRecord.getDelFlag() == 0) {
                return true;
            }

            // 如果记录存在但已删除，则恢复该记录（更新为未删除状态）
            existingRecord.setReason(reason);
            existingRecord.setDelFlag(0);
            existingRecord.setUpdateUserId(currentUser.getUserId());
            existingRecord.setUpdateUserName(currentUser.getUserName());
            existingRecord.setUpdateDate(now);

            return this.updateById(existingRecord);
        } else {
            // 如果记录不存在，则新增记录
            BlackList blackList = new BlackList();
            blackList.setOpenId(openId);
            blackList.setReason(reason);
            blackList.setDelFlag(0);
            blackList.setCreateUserId(currentUser.getUserId());
            blackList.setCreateUserName(currentUser.getUserName());
            blackList.setCreateDate(now);

            return this.save(blackList);
        }
    }

    @Override
    public boolean isInBlackList(String openId) {
        if (!StringUtils.hasText(openId)) {
            return false;
        }

        BlackList blackList = this.getByOpenId(openId);
        return blackList != null && blackList.getDelFlag() == 0;
    }

    public BlackList getByOpenId(String openId) {
        if (!StringUtils.hasText(openId)) {
            return null;
        }
        return this.baseMapper.selectByOpenId(openId);
    }

    @Override
    public boolean softDeleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return false;
        }

        // 获取当前登录用户信息
        SysUser currentUser = SecurityUtils.getLoginUser().getUser();

        return this.baseMapper.softDeleteByIds(ids, currentUser.getUserId(), currentUser.getUserName()) > 0;
    }

}
