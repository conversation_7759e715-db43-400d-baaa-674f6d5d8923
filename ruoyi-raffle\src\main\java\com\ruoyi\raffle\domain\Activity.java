package com.ruoyi.raffle.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/11/6 4:44 下午
 */

@Data
public class Activity {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动规则
     */
    private String activityRule;

    /**
     * 活动介绍
     */
    private String activityDescription;

    /**
     * 活动期数
     */
    private Integer activityNum;

    /**
     * 开奖时间
     */
    private Date raffleOpenTime;
    /**
     * 开奖状态：0-未开奖，1-已开奖
     */
    private Integer raffleStatus;

    /**
     * 活动宣传图
     */
    private String activityPromoPic;

    /**
     * 背景颜色
     */
    private String bgColor;
    /**
     * 是否为深色字体
     */
    private Boolean isDarkFont;

    /**
     * 活动奖品名称
     */
    private String activityPrize;

    /**
     * 活动奖品数量
     */
    private Integer activityPrizeNum;

    /**
     * 活动奖品价值
     */
    private String activityPrizeValue;

    /**
     * 活动奖品图片
     */
    private String activityPrizePic;

    /**
     * 奖品是否需要寄送
     */
    private Integer activityPrizeNeedSent;

    /**
     * 活动链接
     */
    private String activityUrl;

    /**
     * 活动状态：0-待上架、1-活动中，2-已结束
     */
    private Integer status;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人id
     */
    private Long updateId;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 逻辑删除标记
     */
    private Boolean isDeleted;
}
