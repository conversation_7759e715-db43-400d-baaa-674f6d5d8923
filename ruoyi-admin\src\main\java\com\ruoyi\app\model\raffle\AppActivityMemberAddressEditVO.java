package com.ruoyi.app.model.raffle;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "收货地址编辑对象")
public class AppActivityMemberAddressEditVO {

    @ApiModelProperty("活动对象ID")
    private Long activityId;
    @ApiModelProperty("收货人")
    private String receiver;
    @ApiModelProperty("手机号码")
    private String phone;
    @ApiModelProperty("所在区域")
    private String area;
    @ApiModelProperty("详细地址")
    private String address;
}
