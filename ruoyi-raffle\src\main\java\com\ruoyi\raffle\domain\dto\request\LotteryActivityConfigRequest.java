package com.ruoyi.raffle.domain.dto.request;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/7 4:56 下午
 */

@Data
public class LotteryActivityConfigRequest {

    /**
     * 活动信息
     */
    private ActivityInfo activityInfo;

    /**
     * 模板信息
     */
    private List<TemplateInfo> templates;

    /**
     * 奖品日期信息列表
     */
    private List<PrizeDateInfo> prizeDates;


    @Data
    public static class ActivityInfo{
        /**
         * 活动id
         */
        private Long id;

        /**
         * 活动主题
         */
        private String title;

        /**
         * 活动开始时间
         */
        private String startDate;

        /**
         * 活动结束时间
         */
        private String endDate;

        /**
         * 活动简介
         */
        private String introduction;

        /**
         * 活动状态：0-未上架、1-上架中、2-已下架
         */
        private Integer status;

        /**
         * 活动类型 1-按库存抽奖 2-按概率抽奖
         */
        private Integer type;

        /**
         * 每日能抽奖次数(或者每日登录奖励次数)
         */
        private Integer lotteryAmount;

        /**
         * 每日分享获得最大次数
         */
        private Integer shareMaxAmount;

        /**
         * 抽奖消耗今豆配置 0-不消耗 1-每次都消耗 2-每天第一次消耗
         */
        private Integer depleteBeanType;

        /**
         * 抽奖消耗今豆数
         */
        private Integer depleteBean;

        /**
         * 限制设备正常抽奖次数（每日）
         */
        private Integer deviceLimit;

        /**
         * 配置观看时长奖励抽奖次数（观看时长，秒）
         */
        private Integer watchDuration;

        /**
         * 观看时长达标获取抽奖次数 系数
         */
        private Integer watchFactor;

        /**
         * 邀请成功获取抽奖次数 系数
         */
        private Integer inviteFactor;

        /**
         * 邀请成功人数奖励上限
         */
        private Integer inviteMaxAmount;

        /**
         * 邀请成功奖励今豆数
         */
        private Integer inviteRewardBean;

        /**
         * 兜底今豆数-最小值
         */
        private Integer backstopBeanMin;

        /**
         * 兜底今豆数-最大值
         */
        private Integer backstopBeanMax;

        /**
         * 限制ip抽奖 0-不限制 1-限制
         */
        private Integer limitIpAddr;
    }

    @Data
    public static class TemplateInfo{

        /**
         * 模板id
         */
        private Long id;

        /**
         * 奖项id
         */
        private Long itemId;

        /**
         * 奖品类型 0-优先 1-保底
         */
        private Integer prizeType;

        /**
         * 用户中奖策略：0-不限制 1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中
         */
        private Integer userStrategy;
        /**
         * 设备中奖策略：0-不限制 1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中
         */
        private Integer deviceStrategy;

        /**
         * 互斥的奖项id集合
         */
        private List<Long> mutuallyItemIds;

        /**
         * 顺序
         */
        private Integer sort;

        /**
         * 时间段配置
         */
        private List<TimePeriodInfo> timePeriods;
    }

    @Data
    public static class PrizeDateInfo{
        /**
         * 奖品日期
         */
        private String prizeDate;

        /**
         * 奖品列表
         */
        private List<PrizeInfo> prizes;


    }

    @Data
    public static class PrizeInfo{

        /**
         * 奖项id
         */
        private Long itemId;

        /**
         * 奖品类型 0-优先 1-保底
         */
        private Integer prizeType;

        /**
         * 用户中奖策略：0-不限制 1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中
         */
        private Integer userStrategy;
        /**
         * 设备中奖策略：0-不限制 1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中
         */
        private Integer deviceStrategy;

        /**
         * 互斥的奖项id集合
         */
        private List<Long> mutuallyItemIds;

        /**
         * 顺序
         */
        private Integer sort;

        /**
         * 时间段配置
         */
        private List<TimePeriodInfo> timePeriods;

    }


    @Data
    public static class TimePeriodInfo{
        /**
         * 奖品开放开始时间
         */
        private String startTime;

        /**
         * 奖品开放结束时间
         */
        private String endTime;

        /**
         * 数量
         */
        private Integer amount;
    }
}
