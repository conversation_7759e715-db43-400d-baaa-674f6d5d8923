package com.ruoyi.web.controller.lottery;

import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.raffle.domain.LotteryActivity;
import com.ruoyi.raffle.domain.dto.request.LotteryActivityConfigRequest;
import com.ruoyi.raffle.service.ILotteryActivityService;
import com.ruoyi.web.convert.Lottery.LotteryActivityConverter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 抽奖活动表 控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/lottery/activity")
@Api("抽奖活动管理")
public class LotteryActivityController extends BaseController {

    @Resource
    private ILotteryActivityService lotteryActivityService;

    @Resource
    private LotteryActivityConverter lotteryActivityConverter;

    /**
     * 查询抽奖活动列表
     */
    // @PreAuthorize("@ss.hasPermi('lottery:activity:list')")
    @GetMapping("/list")
    @ApiOperation("查询活动列表")
    public TableDataInfo list(LotteryActivity lotteryActivity) {
        startPage();
        LambdaQueryWrapper<LotteryActivity> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(lotteryActivity.getTitle())) {
            queryWrapper.like(LotteryActivity::getTitle, lotteryActivity.getTitle());
        }
        if (lotteryActivity.getStatus() != null) {
            queryWrapper.eq(LotteryActivity::getStatus, lotteryActivity.getStatus());
        }
        if (lotteryActivity.getType() != null) {
            queryWrapper.eq(LotteryActivity::getType, lotteryActivity.getType());
        }
        queryWrapper.eq(LotteryActivity::getDelFlag, 0);
        queryWrapper.orderByDesc(LotteryActivity::getCreateDate);
        List<LotteryActivity> list = lotteryActivityService.list(queryWrapper);
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(e -> {
                List<String> errorMsgList = lotteryActivityService.checkActivityConfig(e.getId());
                if (!CollectionUtils.isEmpty(errorMsgList)) {
                    e.setErrorMessage(String.join("<br>", errorMsgList));
                }
            });
        }

        return getDataTable(list);
    }

    /**
     * 获取抽奖活动详细信息
     */
    // @PreAuthorize("@ss.hasPermi('lottery:activity:query')")
    @GetMapping(value = "/config/{id}")
    @ApiOperation("查询活动配置信息")
    public AjaxResult getConfigInfo(@PathVariable("id") Long id) {
        LotteryActivity activity = lotteryActivityService.getById(id);
        if (Objects.isNull(activity) || !Objects.equals(0, activity.getDelFlag())) {
            return error("活动信息不存在!");
        }

        return success(lotteryActivityConverter.convertConfig(activity));
    }

    /**
     * 新增抽奖活动配置
     */
    // @PreAuthorize("@ss.hasPermi('lottery:activity:add')")
    @Log(title = "抽奖活动配置", businessType = BusinessType.INSERT)
    @PostMapping("/config")
    @ApiOperation("新增抽奖活动配置")
    public AjaxResult saveActivityConfig(@RequestBody LotteryActivityConfigRequest dto) {
        return toAjax(lotteryActivityService.saveActivityConfig(dto));
    }

    /**
     * 修改抽奖活动配置
     */
    // @PreAuthorize("@ss.hasPermi('lottery:activity:edit')")
    @Log(title = "抽奖活动配置", businessType = BusinessType.UPDATE)
    @PutMapping("/config")
    @ApiOperation("修改抽奖活动配置")
    public AjaxResult updateActivityConfig(@RequestBody LotteryActivityConfigRequest dto) {
        return toAjax(lotteryActivityService.updateActivityConfig(dto));
    }

    /**
     * 修改活动信息
     */
    // @PreAuthorize("@ss.hasPermi('lottery:activity:edit')")
    @Log(title = "活动信息", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改活动信息")
    public AjaxResult edit(@RequestBody LotteryActivity lotteryActivity) {
        if (Objects.nonNull(lotteryActivity) && Objects.nonNull(lotteryActivity.getId()) && Objects.equals(lotteryActivity.getStatus(), 1)) {
            List<String> errorMsgList = this.lotteryActivityService.checkActivityConfig(lotteryActivity.getId());
            if (!CollectionUtils.isEmpty(errorMsgList)) {
                return error(String.join("<br>", errorMsgList));
            }
        }
        LotteryActivity entity = new LotteryActivity();
        entity.setId(lotteryActivity.getId());
        if (Objects.nonNull(lotteryActivity.getStatus())) {
            entity.setStatus(lotteryActivity.getStatus());
        }
        if (StringUtils.isNotBlank(lotteryActivity.getTitle())) {
            entity.setTitle(lotteryActivity.getTitle());
        }
        entity.setWeight(lotteryActivity.getWeight());
        entity.setDelFlag(lotteryActivity.getDelFlag());
        entity.setIntroduction(lotteryActivity.getIntroduction());
        entity.setUpdateUserId(getLoginUser().getUserId());
        entity.setUpdateUserName(getLoginUser().getUsername());
        entity.setUpdateDate(DateUtils.getNowDate());
        return toAjax(this.lotteryActivityService.updateById(entity));
    }
}
