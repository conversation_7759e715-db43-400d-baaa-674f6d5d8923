package com.ruoyi.web.convert.Lottery;

import com.alibaba.fastjson2.util.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.raffle.domain.*;
import com.ruoyi.raffle.domain.dto.LotteryPrizeDTO;
import com.ruoyi.raffle.domain.dto.LotteryTemplateInfoDTO;
import com.ruoyi.raffle.domain.dto.request.ActivityPageRequest;
import com.ruoyi.raffle.domain.dto.response.LotteryActivityConfigResponse;
import com.ruoyi.raffle.service.*;
import com.ruoyi.third.party.service.IOssFileService;
import com.ruoyi.web.model.raffle.ActivityMemberAddressVO;
import com.ruoyi.web.model.raffle.ActivityMemberVO;
import com.ruoyi.web.model.raffle.ActivityPageQueryVO;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class LotteryActivityConverter {

    @Resource
    private ILotteryPrizeService lotteryPrizeService;

    @Resource
    private ILotteryPrizeTimePeriodService lotteryPrizeTimePeriodService;

    @Resource
    private ILotteryPrizeTemplateService lotteryPrizeTemplateService;

    @Resource
    private ILotteryPrizeTemplateTimePeriodService lotteryPrizeTemplateTimePeriodService;

    @Resource
    private IOssFileService ossFileService;

    public LotteryActivityConfigResponse convertConfig(LotteryActivity activity) {
        if (Objects.isNull(activity)){
            return null;
        }
        LotteryActivityConfigResponse response = new LotteryActivityConfigResponse();
        response.setActivityInfo(covertActivity(activity));
        response.setTemplates(covertTemplate(activity.getId()));
        response.setPrizeDates(covertPrizeDate(activity));
        return response;

    }

    private LotteryActivityConfigResponse.ActivityInfo covertActivity(LotteryActivity activity){
        LotteryActivityConfigResponse.ActivityInfo activityInfo = new LotteryActivityConfigResponse.ActivityInfo();
        BeanUtils.copyProperties(activity, activityInfo);
        activityInfo.setStartDate(activity.getStartDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        activityInfo.setEndDate(activity.getEndDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        return activityInfo;
    }

    private List<LotteryActivityConfigResponse.TemplateInfo> covertTemplate(Long activityId){
        List<LotteryTemplateInfoDTO> templateList = this.lotteryPrizeTemplateService.queryTemplateInfoList(activityId);
        if (CollectionUtils.isEmpty(templateList)){
            return Collections.emptyList();
        }

        // 批量查询所有模板的时间段配置，减少数据库查询次数
        List<Long> templateIds = templateList.stream().map(LotteryTemplateInfoDTO::getId).collect(Collectors.toList());

        Map<Long, List<LotteryPrizeTemplateTimePeriod>> timePeriodMap = batchQueryTimePeriods(templateIds);

        return templateList.stream().map(template -> {
            LotteryActivityConfigResponse.TemplateInfo templateInfo = new LotteryActivityConfigResponse.TemplateInfo();
            BeanUtils.copyProperties(template, templateInfo);
            templateInfo.setCoverUrl(ossFileService.getOriUrl(template.getCover()));

            // 解析互斥奖项ID
            templateInfo.setMutuallyItemIds(parseMutuallyItemIds(template.getMutuallyItemIds()));

            // 从批量查询结果中获取时间段配置
            List<LotteryPrizeTemplateTimePeriod> timePeriodList = timePeriodMap.getOrDefault(template.getId(), Collections.emptyList());
            if (!CollectionUtils.isEmpty(timePeriodList)){
                List<LotteryActivityConfigResponse.TimePeriodInfo> timePeriodInfos = convertTimePeriods(timePeriodList);
                templateInfo.setAmount(timePeriodList.stream().mapToInt(LotteryPrizeTemplateTimePeriod::getAmount).sum());
                templateInfo.setTimePeriods(timePeriodInfos);
            }

            return templateInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 批量查询时间段配置，减少数据库查询次数
     */
    private Map<Long, List<LotteryPrizeTemplateTimePeriod>> batchQueryTimePeriods(List<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return Collections.emptyMap();
        }

        List<LotteryPrizeTemplateTimePeriod> allTimePeriods = this.lotteryPrizeTemplateTimePeriodService.list(
            new LambdaQueryWrapper<LotteryPrizeTemplateTimePeriod>()
                .in(LotteryPrizeTemplateTimePeriod::getTemplateId, templateIds)
                .orderByAsc(LotteryPrizeTemplateTimePeriod::getTemplateId)
                .orderByAsc(LotteryPrizeTemplateTimePeriod::getStartTime)
        );

        return allTimePeriods.stream()
                .collect(Collectors.groupingBy(LotteryPrizeTemplateTimePeriod::getTemplateId));
    }

    /**
     * 解析互斥奖项ID字符串
     */
    private List<Long> parseMutuallyItemIds(String mutuallyItemIds) {
        if (!StringUtils.hasText(mutuallyItemIds)) {
            return Collections.emptyList();
        }

        try {
            return Arrays.stream(mutuallyItemIds.split(","))
                    .map(String::trim)
                    .filter(StringUtils::hasText)
                    .map(Long::valueOf)
                    .collect(Collectors.toList());
        } catch (NumberFormatException e) {
            // 记录日志并返回空列表
            System.err.println("解析互斥奖项ID失败: " + mutuallyItemIds);
            return Collections.emptyList();
        }
    }

    /**
     * 转换时间段配置
     */
    private List<LotteryActivityConfigResponse.TimePeriodInfo> convertTimePeriods(List<LotteryPrizeTemplateTimePeriod> timePeriodList) {
        return timePeriodList.stream().map(timePeriod -> {
            LotteryActivityConfigResponse.TimePeriodInfo timePeriodInfo = new LotteryActivityConfigResponse.TimePeriodInfo();
            timePeriodInfo.setStartTime(timePeriod.getStartTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            timePeriodInfo.setEndTime(timePeriod.getEndTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            timePeriodInfo.setAmount(timePeriod.getAmount());
            return timePeriodInfo;
        }).collect(Collectors.toList());
    }

    private List<LotteryActivityConfigResponse.PrizeDateInfo> covertPrizeDate(LotteryActivity lotteryActivity){
        // 计算活动总天数
        long daysBetween = ChronoUnit.DAYS.between(lotteryActivity.getStartDate(), lotteryActivity.getEndDate()) + 1; // +1 包含结束日期

        List<LotteryActivityConfigResponse.PrizeDateInfo> prizeDateList = new ArrayList<>((int) daysBetween);

        // 批量查询活动期间的所有奖品数据，避免在循环中多次查询数据库
        Map<String, List<LotteryPrizeDTO>> prizeDataMap = batchQueryAllPrizeData(lotteryActivity.getId(), lotteryActivity.getStartDate().toLocalDate(), lotteryActivity.getEndDate().toLocalDate());

        // 循环生成活动期间的每一天
        for (int i = 0; i < daysBetween; i++) {
            LocalDate currentDate = lotteryActivity.getStartDate().toLocalDate().plusDays(i);
            String prizeDate = currentDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));

            LotteryActivityConfigResponse.PrizeDateInfo prizeDateInfo = new LotteryActivityConfigResponse.PrizeDateInfo();
            prizeDateInfo.setPrizeDate(prizeDate);

            // 从批量查询结果中获取当天的奖品数据
            List<LotteryPrizeDTO> dayPrizes = prizeDataMap.getOrDefault(prizeDate, Collections.emptyList());
            prizeDateInfo.setPrizes(convertPrizeList(dayPrizes));

            prizeDateList.add(prizeDateInfo);
        }
        return prizeDateList;
    }

    /**
     * 批量查询活动期间的所有奖品数据
     */
    private Map<String, List<LotteryPrizeDTO>> batchQueryAllPrizeData(Long activityId, LocalDate startDate, LocalDate endDate) {
        // 生成日期范围内的所有日期
        List<String> dateList = new ArrayList<>();
        long daysBetween = ChronoUnit.DAYS.between(startDate, endDate) + 1;
        for (int i = 0; i < daysBetween; i++) {
            String dateStr = startDate.plusDays(i).format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            dateList.add(dateStr);
        }

        // 批量查询所有日期的奖品数据
        Map<String, List<LotteryPrizeDTO>> prizeDataMap = new HashMap<>();
        for (String date : dateList) {
            List<LotteryPrizeDTO> prizes = this.lotteryPrizeService.queryPrizePools(activityId, date);
            prizeDataMap.put(date, !CollectionUtils.isEmpty(prizes) ? prizes : Collections.emptyList());
        }

        return prizeDataMap;
    }

    /**
     * 批量查询奖品时间段配置
     */
    private Map<Long, List<LotteryPrizeTimePeriod>> batchQueryPrizeTimePeriods(List<Long> prizeIds) {
        if (CollectionUtils.isEmpty(prizeIds)) {
            return Collections.emptyMap();
        }

        List<LotteryPrizeTimePeriod> allTimePeriods = this.lotteryPrizeTimePeriodService.list(
            new LambdaQueryWrapper<LotteryPrizeTimePeriod>()
                .in(LotteryPrizeTimePeriod::getPrizeId, prizeIds)
                .orderByAsc(LotteryPrizeTimePeriod::getPrizeId)
                .orderByAsc(LotteryPrizeTimePeriod::getStartTime)
        );

        return allTimePeriods.stream()
                .collect(Collectors.groupingBy(LotteryPrizeTimePeriod::getPrizeId));
    }

    /**
     * 转换奖品列表
     */
    private List<LotteryActivityConfigResponse.PrizeInfo> convertPrizeList(List<LotteryPrizeDTO> prizeList) {
        if (CollectionUtils.isEmpty(prizeList)) {
            return Collections.emptyList();
        }

        // 批量查询所有奖品的时间段配置
        List<Long> prizeIds = prizeList.stream().map(LotteryPrizeDTO::getId).collect(Collectors.toList());

        Map<Long, List<LotteryPrizeTimePeriod>> prizeTimePeriodMap = batchQueryPrizeTimePeriods(prizeIds);

        return prizeList.stream().map(prize -> {
            LotteryActivityConfigResponse.PrizeInfo prizeInfo = new LotteryActivityConfigResponse.PrizeInfo();
            BeanUtils.copyProperties(prize, prizeInfo);
            prizeInfo.setCoverUrl(ossFileService.getOriUrl(prize.getCover()));

            // 解析互斥奖项ID
            prizeInfo.setMutuallyItemIds(parseMutuallyItemIds(prize.getMutuallyItemIds()));

            // 处理时间段配置
            List<LotteryPrizeTimePeriod> timePeriodList = prizeTimePeriodMap.getOrDefault(prize.getId(), Collections.emptyList());
            if (!CollectionUtils.isEmpty(timePeriodList)) {
                List<LotteryActivityConfigResponse.TimePeriodInfo> timePeriodInfos = convertPrizeTimePeriods(timePeriodList);
                prizeInfo.setAmount(timePeriodList.stream().mapToInt(LotteryPrizeTimePeriod::getAmount).sum());
                prizeInfo.setIssueAmount(timePeriodList.stream().mapToInt(LotteryPrizeTimePeriod::getIssueAmount).sum());
                prizeInfo.setTimePeriods(timePeriodInfos);
            }

            return prizeInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 转换奖品时间段配置
     */
    private List<LotteryActivityConfigResponse.TimePeriodInfo> convertPrizeTimePeriods(List<LotteryPrizeTimePeriod> timePeriodList) {
        return timePeriodList.stream().map(timePeriod -> {
            LotteryActivityConfigResponse.TimePeriodInfo timePeriodInfo = new LotteryActivityConfigResponse.TimePeriodInfo();
            timePeriodInfo.setStartTime(timePeriod.getStartTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            timePeriodInfo.setEndTime(timePeriod.getEndTime().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
            timePeriodInfo.setAmount(timePeriod.getAmount());
            timePeriodInfo.setIssueAmount(timePeriod.getIssueAmount());
            return timePeriodInfo;
        }).collect(Collectors.toList());
    }

}
