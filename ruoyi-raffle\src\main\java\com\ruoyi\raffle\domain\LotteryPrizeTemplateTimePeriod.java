package com.ruoyi.raffle.domain;

import java.io.Serializable;
import java.time.LocalTime;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 抽奖奖品模板时间控制表实体类 用于配置奖品模板在特定时间段内的投放数量模板
 * 
 * <AUTHOR>
 * @date 2023-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lottery_prize_template_time_period")
public class LotteryPrizeTemplateTimePeriod implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模板ID
     */
    @TableField("template_id")
    private Long templateId;

    /**
     * 奖品开放开始时间
     */
    @TableField("start_time")
    private LocalTime startTime;

    /**
     * 奖品开放结束时间
     */
    @TableField("end_time")
    private LocalTime endTime;

    /**
     * 数量
     */
    @TableField("amount")
    private Integer amount;

    /**
     * 创建用户id
     */
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 创建用户账号
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 更新用户id
     */
    @TableField("update_user_id")
    private Long updateUserId;

    /**
     * 更新用户账号
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField("update_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}
