package com.ruoyi.raffle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.raffle.domain.Activity;
import com.ruoyi.raffle.domain.ActivityMember;
import com.ruoyi.raffle.domain.dto.request.ActivityApplyRequest;
import com.ruoyi.raffle.domain.dto.request.ActivityWinRequest;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/6 4:57 下午
 */
public interface IActivityMemberService extends IService<ActivityMember> {
    /**
     * 获取中奖成员列表
     *
     * @param request
     * @return
     */
    List<ActivityMember> getWinMembers(ActivityWinRequest request);

    Integer getApplyCount(Long activityId);

    Long activityApply(ActivityApplyRequest request);

    ActivityMember getByActivityIdAndUserId(Long activityId, Long userId);


    Integer getRaffleStatus(Long activityId, Long userId);


    boolean exchangeStatusChange(Long memberId, Integer status);

    String genRaffleCode(Activity activity);
    boolean updateRaffleCode(Long activityId, String codePrefix);
}
