package com.ruoyi.third.party.service.impl;

import java.util.HashMap;
import java.util.Map;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.utils.RaffleSignUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.third.party.domain.dto.request.jsp.JspLotteryOperateRequest;
import com.ruoyi.third.party.domain.dto.request.jsp.JspNotificationRequest;
import com.ruoyi.third.party.domain.dto.response.jsp.JspLotteryOperateResponse;
import com.ruoyi.third.party.domain.dto.response.jsp.JspNotificationResponse;
import com.ruoyi.third.party.domain.dto.response.jsp.JspUserResponse;
import com.ruoyi.third.party.service.IJspService;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class JspServiceImpl implements IJspService {

    @Value("${jsp.user-info-url}")
    private String userInfoUrl;

    @Value("${jsp.notification-url}")
    private String notificationUrl;

    @Value("${jsp.lottery-operate-url}")
    private String lotteryOperateUrl;


    @Resource
    private RestTemplate restTemplate;

    @Override
    public JspUserResponse getUserInfo(String token) {
        //  今视频平台
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Bearer " + token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 构造HttpEntity对象，将headers作为参数传入
        HttpEntity<Object> entity = new HttpEntity<>(null, headers);
        // 发起GET请求，获取返回结果
        ResponseEntity<String> response = restTemplate.exchange(userInfoUrl, HttpMethod.GET, entity, String.class);
        // 输出返回结果
        String body = response.getBody();
        //  判断返回结果
        return JSON.parseObject(body, JspUserResponse.class);
    }

    @Override
    public JspNotificationResponse sendNotification(JspNotificationRequest request) {
        return  restTemplate.postForObject(notificationUrl, request, JspNotificationResponse.class);
    }

    @Override
    public JspLotteryOperateResponse lotteryOperate(String token,String openId, JspLotteryOperateRequest request) {
        HttpHeaders headers = new HttpHeaders();
        headers.add("Authorization", "Bearer " + token);
        headers.setContentType(MediaType.APPLICATION_JSON);

        request.setSeqId(IdUtils.simpleUUID());
        request.setT(System.currentTimeMillis() / 1000);



        Map<String, Object> params = new HashMap<>(10);
        params.put("activityId", request.getActivityId());
        params.put("seqId", request.getSeqId());
        params.put("openId", openId);
        params.put("logId", request.getLogId());
        params.put("operateType", request.getOperateType());
        params.put("type", request.getType());
        params.put("price", request.getPrice().toPlainString());
        params.put("t", request.getT());

        String sign = RaffleSignUtils.sign(params, RaffleSignUtils.SMS_SECRET, request.getT());
        params.put("sign", sign);
        params.put("title",request.getTitle());

        // 构造HttpEntity对象，将headers作为参数传入
        HttpEntity<Object> entity = new HttpEntity<>(JSON.toJSONString(params), headers);

        return restTemplate.postForObject(lotteryOperateUrl, entity, JspLotteryOperateResponse.class);
    }
}
