package com.ruoyi.raffle.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.raffle.domain.ActivityMember;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date 2023/11/6 4:56 下午
 */

public interface ActivityMemberMapper extends BaseMapper<ActivityMember> {
    int updateRaffleCode(@Param("activityId") Long activityId, @Param("codePrefix") String codePrefix);
}
