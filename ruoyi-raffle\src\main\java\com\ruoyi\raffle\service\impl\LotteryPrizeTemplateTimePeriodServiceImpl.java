package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.raffle.domain.LotteryPrizeTemplateTimePeriod;
import com.ruoyi.raffle.mapper.LotteryPrizeTemplateTimePeriodMapper;
import com.ruoyi.raffle.service.ILotteryPrizeTemplateTimePeriodService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 抽奖奖品模板时间控制表业务逻辑实现类
 * 实现奖品模板时间控制相关的业务操作
 * 
 * <AUTHOR>
 * @date 2023-12-18
 */
@Slf4j
@Service
public class LotteryPrizeTemplateTimePeriodServiceImpl extends ServiceImpl<LotteryPrizeTemplateTimePeriodMapper, LotteryPrizeTemplateTimePeriod> 
        implements ILotteryPrizeTemplateTimePeriodService {


}
