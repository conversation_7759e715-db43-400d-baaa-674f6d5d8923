package com.ruoyi.raffle.service;

import java.util.List;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.raffle.domain.LotteryActivity;
import com.ruoyi.raffle.domain.dto.request.LotteryActivityConfigRequest;

/**
 * 抽奖活动表 服务接口
 * 
 * <AUTHOR>
 */
public interface ILotteryActivityService extends IService<LotteryActivity> {

    boolean saveActivityConfig(LotteryActivityConfigRequest request);

    boolean updateActivityConfig(LotteryActivityConfigRequest request);

    List<String> checkActivityConfig(Long activityId);

    int calculateTotalStock(Long activityId, Long itemId);
}
