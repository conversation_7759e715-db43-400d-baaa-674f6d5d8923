package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.user.UserNotExistsException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.raffle.domain.Activity;
import com.ruoyi.raffle.mapper.ActivityMapper;
import com.ruoyi.raffle.domain.dto.request.ActivityPageRequest;
import com.ruoyi.raffle.domain.dto.response.ActivityQueryResponse;
import com.ruoyi.raffle.service.IActivityService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/11/6 4:58 下午
 */

@Service
public class ActivityServiceImpl extends ServiceImpl<ActivityMapper, Activity> implements IActivityService {

    @Override
    public List<Activity> queryActiveList(String tenantId) {
        return this.baseMapper.queryActiveList(tenantId);
    }

    @Override
    public List<Activity> listByCondition(ActivityPageRequest request) {
        LambdaQueryWrapper<Activity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.like(StringUtils.isNotEmpty(request.getActivityName()), Activity::getActivityName, request.getActivityName());
        queryWrapper.in(ObjectUtils.isNotEmpty(request.getStatuses()), Activity::getStatus, request.getStatuses());
        queryWrapper.ge(ObjectUtils.isNotEmpty(request.getCreateDateStart()), Activity::getCreateDate, request.getCreateDateStart());
        queryWrapper.le(ObjectUtils.isNotEmpty(request.getCreateDateEnd()), Activity::getCreateDate, request.getCreateDateEnd());
        queryWrapper.ge(ObjectUtils.isNotEmpty(request.getRaffleOpenTimeStart()), Activity::getRaffleOpenTime, request.getRaffleOpenTimeStart());
        queryWrapper.le(ObjectUtils.isNotEmpty(request.getRaffleOpenTimeEnd()), Activity::getRaffleOpenTime, request.getRaffleOpenTimeEnd());
        queryWrapper.eq(ObjectUtils.isNotEmpty(request.getTenantId()), Activity::getTenantId, request.getTenantId());
        queryWrapper.eq(Activity::getIsDeleted, false);

        List<ActivityPageRequest.Sort> sorts = request.getSorts();
        if (!CollectionUtils.isEmpty(sorts)) {
            for (ActivityPageRequest.Sort sort : sorts) {
                //  创建时间排序
                queryWrapper.orderBy("createDate".equals(sort.getOrderBy()), sort.isAsc(), Activity::getCreateDate);
                //  状态排序
                queryWrapper.orderBy("status".equals(sort.getOrderBy()), sort.isAsc(), Activity::getStatus);
                //  活动期数
                queryWrapper.orderBy("activityNum".equals(sort.getOrderBy()), sort.isAsc(), Activity::getActivityNum);
                //  活动期数
                queryWrapper.orderBy("raffleOpenTime".equals(sort.getOrderBy()), sort.isAsc(), Activity::getRaffleOpenTime);
            }
        }

        return this.list(queryWrapper);
    }

    @Override
    public void activeSave(Activity activity) {
        SysUser user = SecurityUtils.getLoginUser().getUser();

        if (Objects.nonNull(activity.getId())) {
            Activity entity = this.getById(activity.getId());
            if (Objects.isNull(entity)) {
                throw new UserNotExistsException();
            }

            //  已经结束的互动不支持编辑
            if (Objects.equals(entity.getStatus(), 2)) {
                throw new UserNotExistsException();
            }
            //  活动上架之后，开奖时间不能编辑
            if (Objects.equals(activity.getStatus(), 1)) {
                if (activity.getRaffleOpenTime().before(entity.getRaffleOpenTime())) {
                    throw new UserNotExistsException();
                }
            }

            activity.setUpdateId(user.getUserId());
            activity.setUpdateDate(new Date());
            activity.setUpdateName(user.getNickName());

            this.updateById(activity);
        } else {
            activity.setActivityNum(getNextActivityNum(user.getTenantId()));
            activity.setTenantId(user.getTenantId());
            activity.setCreateId(user.getUserId());
            activity.setCreateDate(new Date());
            activity.setCreateName(user.getNickName());
            activity.setStatus(0);
            activity.setIsDeleted(false);

            this.save(activity);
        }
    }

    private int getNextActivityNum(String tenantId) {
        LambdaQueryWrapper<Activity> activityQuery = Wrappers.lambdaQuery();
        activityQuery.eq(Activity::getTenantId, tenantId);
        activityQuery.orderByDesc(Activity::getActivityNum);
        activityQuery.last(" limit 1");

        Activity one = this.getOne(activityQuery);
        return Objects.isNull(one) ? 1 : one.getActivityNum() + 1;
    }


    @Override
    public Activity activeQueryById(Long id) {
        return this.getById(id);
    }


    @Override
    public void activityStatusChange(Long activityId, Integer status) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Activity activity = this.getById(activityId);
        activity.setStatus(status);
        activity.setUpdateId(user.getUserId());
        activity.setUpdateDate(new Date());
        activity.setUpdateName(user.getNickName());
        this.updateById(activity);

    }

    @Override
    public List<Activity> queryPublicActivityList(String tenantId) {
        return this.baseMapper.queryPublicActivityList(tenantId);
    }

    @Override
    public Activity queryOneActivity(String tenantId, Long s) {
        LambdaQueryWrapper<Activity> activityQuery = Wrappers.lambdaQuery();
        activityQuery.eq(Objects.nonNull(tenantId), Activity::getTenantId, tenantId);
        activityQuery.lt(Activity::getCreateDate, new Date(s));
        activityQuery.gt(Activity::getRaffleOpenTime, new Date(s));
        activityQuery.last("  ORDER BY RAND() LIMIT 1 ");
        return this.getOne(activityQuery);
    }
}
