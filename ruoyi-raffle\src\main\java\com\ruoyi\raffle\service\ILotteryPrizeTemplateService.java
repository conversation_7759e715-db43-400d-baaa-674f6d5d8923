package com.ruoyi.raffle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.raffle.domain.LotteryPrizeTemplate;
import com.ruoyi.raffle.domain.dto.LotteryTemplateInfoDTO;

import java.util.List;

/**
 * 抽奖奖品模板表业务逻辑接口
 * 提供奖品模板相关的业务操作方法
 * 
 * <AUTHOR>
 * @date 2023-12-18
 */
public interface ILotteryPrizeTemplateService extends IService<LotteryPrizeTemplate> {


    List<LotteryTemplateInfoDTO> queryTemplateInfoList(Long activityId);
}
