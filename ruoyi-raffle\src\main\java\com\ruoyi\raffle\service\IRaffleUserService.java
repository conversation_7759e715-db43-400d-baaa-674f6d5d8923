package com.ruoyi.raffle.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.raffle.domain.RaffleUser;

/**
 * <AUTHOR>
 * @date 2023/12/04 4:57 下午
 */
public interface IRaffleUserService extends IService<RaffleUser> {

    RaffleUser getByTenantIdAndOpenId(String tenantId, String openId);

    RaffleUser getBySysUserId(Long sysUserId);

    /**
     * 订阅/取消 新活动通知
     *
     * @param userId      用户id
     * @param isSubscribe 是否订阅
     * @return 订阅结果
     */
    Boolean subscribeNewActivityNotification(Long userId, Boolean isSubscribe);


}
