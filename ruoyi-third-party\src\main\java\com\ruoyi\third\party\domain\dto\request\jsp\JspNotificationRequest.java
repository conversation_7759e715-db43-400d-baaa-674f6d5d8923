package com.ruoyi.third.party.domain.dto.request.jsp;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class JspNotificationRequest implements Serializable {

    //通知ID
    private Long id;
    //通知类型
    private Integer fromType;
    //发送认证号ID
    private Long fromId;
    //发送认证号
    private String fromName;
    //接收用户类别
    private Integer toType;
    //接收用户ID集合
    private List<Long> toIds;
    //通知标题
    private String title;
    //简介
    private String intro;
    //详情
    private String detail;
    //模版类型
    private Integer type;
    //按钮文本或图片ID
    private String extra;
    //跳转URL
    private String outerUrl;
    //图片URL回显用
    private String imgUrl;
    //提交通知用户名
    private String adminUser;
}
