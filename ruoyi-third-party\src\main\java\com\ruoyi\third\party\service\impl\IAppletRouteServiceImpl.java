package com.ruoyi.third.party.service.impl;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.third.party.domain.dto.request.AppletRouteQueryRequest;
import com.ruoyi.third.party.domain.dto.response.AppletRouteQueryResponse;
import com.ruoyi.third.party.service.IAppletRouteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class IAppletRouteServiceImpl implements IAppletRouteService {

    @Value("${applet.route-url}")
    private String appletRouteUrl;

    @Value("${applet.package-id}")
    private String packageId;

    @Value("${applet.env}")
    private Integer env;

    @Resource
    private RestTemplate restTemplate;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private final static String cache_key = "cache::route::info::%s-%s-%s-%s";


    @Override
    public AppletRouteQueryResponse getRouteList(Long activityId, String path) {

        String cacheKey = String.format(cache_key, activityId, path, env, packageId);
        String cacheValue = stringRedisTemplate.opsForValue().get(cacheKey);
        if (StringUtils.hasText(cacheValue)) {
            //  命中缓存直接返回
            return JSON.parseObject(cacheValue, AppletRouteQueryResponse.class);

        } else {
            //  发送远程请求
            long eventId = System.currentTimeMillis() / 1000;

            AppletRouteQueryRequest request = new AppletRouteQueryRequest();
            request.setEventId(eventId);
            request.setVersion("1.0");
            request.setCaller("test");
            request.setSeqId(String.valueOf(eventId));
            request.setTimestamp(eventId);

            AppletRouteQueryRequest.InterfaceDTO interfaceDTO = new AppletRouteQueryRequest.InterfaceDTO();
            AppletRouteQueryRequest.InterfaceDTO.ParamDTO paramDTO = new AppletRouteQueryRequest.InterfaceDTO.ParamDTO();

            if (Objects.nonNull(activityId)) {
                AppletRouteQueryRequest.InterfaceDTO.ParamDTO.QueryDTO queryDTO = new AppletRouteQueryRequest.InterfaceDTO.ParamDTO.QueryDTO();
                queryDTO.setActivityId(activityId);

                paramDTO.setQuery(queryDTO);
            }


            paramDTO.setPath(path);
            paramDTO.setEnv(env);
            paramDTO.setPackageId(packageId);

            interfaceDTO.setParam(paramDTO);
            interfaceDTO.setName("common.applet.getAppletJumpUrlList");

            request.setInterfaceX(interfaceDTO);


            AppletRouteQueryResponse response = restTemplate.postForObject(appletRouteUrl, request, AppletRouteQueryResponse.class);

            //  响应成功，存入缓存
            if (Objects.nonNull(response) && Objects.equals(response.getCode(), 200)) {

                cacheValue = JSON.toJSONString(response);

                stringRedisTemplate.opsForValue().set(cacheKey, cacheValue, 1, TimeUnit.HOURS);
            }

            return response;

        }


    }
}
