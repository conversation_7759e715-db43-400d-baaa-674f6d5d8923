package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.raffle.domain.LotteryPrize;
import com.ruoyi.raffle.domain.dto.LotteryPrizeDTO;
import com.ruoyi.raffle.mapper.LotteryPrizeMapper;
import com.ruoyi.raffle.service.ILotteryPrizeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 抽奖奖品表 服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class LotteryPrizeServiceImpl extends ServiceImpl<LotteryPrizeMapper, LotteryPrize> implements ILotteryPrizeService {
    @Override
    public List<LotteryPrizeDTO> queryPrizeList(Long activityId,String activityDate,Boolean isLimitCurrentTime) {
        return this.baseMapper.queryPrizeList(activityId,activityDate,isLimitCurrentTime);
    }

    @Override
    public List<LotteryPrizeDTO> queryPrizePools(Long activityId, String activityDate) {
        return this.baseMapper.queryPrizePools(activityId,activityDate);
    }
}