package com.ruoyi.web.model.raffle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class ActivityMemberVO {


    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 抽奖码
     */
    private String raffleCode;

    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动奖品名称
     */
    private String activityPrize;

    /**
     * 中奖时间
     */
    private Date winTime;

    /**
     * 中奖人手机号
     */
    private String phone;

    /**
     * 中奖人昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 抽奖状态：0-未中奖，1-已中奖
     */
    private Integer raffleStatus;
    /**
     * 兑换状态：0-未兑奖，1-已兑奖
     */
    private Integer exchangeStatus;

    /**
     * 成员状态：0-无效，1-有效
     */
    private Integer status;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人id
     */
    private Long updateId;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 逻辑删除标记
     */
    private Boolean isDeleted;


    /**
     * 地址信息
     */
    private ActivityMemberAddressVO address;

}
