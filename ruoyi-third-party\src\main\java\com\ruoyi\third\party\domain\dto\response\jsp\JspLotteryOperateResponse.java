package com.ruoyi.third.party.domain.dto.response.jsp;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@NoArgsConstructor
@Data
public class JspLotteryOperateResponse implements Serializable {

    @JsonProperty("code")
    private Integer code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("result")
    private Boolean result;
    @JsonProperty("timestamp")
    private Long timestamp;
}
