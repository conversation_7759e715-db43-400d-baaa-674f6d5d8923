package com.ruoyi.third.party.service;


import com.ruoyi.third.party.domain.dto.request.jsp.JspLotteryOperateRequest;
import com.ruoyi.third.party.domain.dto.request.jsp.JspNotificationRequest;
import com.ruoyi.third.party.domain.dto.response.jsp.JspLotteryOperateResponse;
import com.ruoyi.third.party.domain.dto.response.jsp.JspNotificationResponse;
import com.ruoyi.third.party.domain.dto.response.jsp.JspUserResponse;

public interface IJspService {

    JspUserResponse getUserInfo(String token);

    JspNotificationResponse sendNotification(JspNotificationRequest request);

    JspLotteryOperateResponse lotteryOperate(String token,String openId, JspLotteryOperateRequest request);
}
