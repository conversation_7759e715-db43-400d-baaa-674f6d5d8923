package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.raffle.domain.RaffleUser;
import com.ruoyi.raffle.mapper.RaffleUserMapper;
import com.ruoyi.raffle.service.IRaffleUserService;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/04 4:58 下午
 */

@Service
public class RaffleUserServiceImpl extends ServiceImpl<RaffleUserMapper, RaffleUser> implements IRaffleUserService {


    @Override
    public RaffleUser getByTenantIdAndOpenId(String tenantId, String openId) {
        LambdaQueryWrapper<RaffleUser> userQuery = Wrappers.lambdaQuery();
        userQuery.eq(RaffleUser::getTenantId, tenantId);
        userQuery.eq(RaffleUser::getOpenId, openId);

        return this.getOne(userQuery);
    }

    @Override
    public RaffleUser getBySysUserId(Long sysUserId) {
        LambdaQueryWrapper<RaffleUser> userQuery = Wrappers.lambdaQuery();
        userQuery.eq(RaffleUser::getUserId, sysUserId);

        return this.getOne(userQuery);
    }

    @Override
    public Boolean subscribeNewActivityNotification(Long userId, Boolean isSubscribe) {
        RaffleUser raffleUser = this.getBySysUserId(userId);
        if (Objects.nonNull(raffleUser)) {
            raffleUser.setIsSubscribeNewActivityNotification(isSubscribe);
            return this.updateById(raffleUser);
        }
        return false;
    }


}
