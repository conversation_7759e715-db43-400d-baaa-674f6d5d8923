
package com.ruoyi.app.model.lottery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户抽奖结果
 * 
 * <AUTHOR>
 * @date 2024/1/30 11:04
 */
@Data
@ApiModel
public class LotteryResultVO implements Serializable {

    @ApiModelProperty("奖品ID")
    private Long id;

    @ApiModelProperty("奖项ID(用于匹配转盘指针指向)")
    private Long itemId;

    @ApiModelProperty("类型: 1-现金 2-今豆 3-物品")
    private Integer type;

    @ApiModelProperty("奖品名称")
    private String name;

    @ApiModelProperty("奖品图片")
    private String coverUrl;

    @ApiModelProperty("奖品值 type=1时为抽中的现金红包值 type=2时为抽中的今豆数 type=3时为物品数量（默认1份）")
    private String prizeVal;

    @ApiModelProperty("抽奖日志ID")
    private Long logId;

    @ApiModelProperty("物品券码（如果有）")
    private String couponCode;

    @ApiModelProperty("绑定手机号（抽中物品时当需要绑定手机号）")
    private String bindMobile;

    @ApiModelProperty("type=3为物品时,是否需要绑定手机号 0-不需要 1-需要 2-使用当前账户的手机号")
    private Integer bindMobileFlag;

    @ApiModelProperty("type=3为物品时,是否需要绑定姓名 0-不需要 1-需要")
    private Integer bindNameFlag;
}
