package com.ruoyi.third.party.domain.dto.request.jsp;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class JspLotteryOperateRequest implements Serializable {

    //序列号
    private String seqId;
    //活动Id
    private Long  activityId;
    //类型 1-今豆 2-钱包
    private Integer type;
    //操作类型 1-添加 2-扣减
    private Integer operateType;
    //数值
    private BigDecimal price;
    //日志Id
    private Long logId;
    //活动名称
    private String title;
    //时间戳不能为空
    private Long t;
    //签名
    private String sign;

    public JspLotteryOperateRequest() {

    }
    public JspLotteryOperateRequest(Long  activityId,Integer type, Integer operateType, BigDecimal price, Long logId, String title) {
        this.activityId = activityId;
        this.type = type;
        this.operateType = operateType;
        this.price = price;
        this.logId = logId;
        this.title = title;
    }
}
