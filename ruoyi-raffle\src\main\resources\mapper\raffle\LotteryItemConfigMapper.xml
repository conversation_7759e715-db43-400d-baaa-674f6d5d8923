<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!--
    抽奖奖品配置表数据访问层映射文件
    提供奖品配置相关的SQL映射配置
    
    <AUTHOR>
    @date 2023-12-19
-->
<mapper namespace="com.ruoyi.raffle.mapper.LotteryItemConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.raffle.domain.LotteryItemConfig">
        <id column="id" property="id" />
        <result column="activity_id" property="activityId" />
        <result column="item_id" property="itemId" />
        <result column="bind_mobile_flag" property="bindMobileFlag" />
        <result column="bind_name_flag" property="bindNameFlag" />
        <result column="mobile_unique_flag" property="mobileUniqueFlag" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_date" property="createDate" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_date" property="updateDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, activity_id, item_id, bind_mobile_flag, bind_name_flag, mobile_unique_flag,
        create_user_id, create_user_name, create_date, 
        update_user_id, update_user_name, update_date
    </sql>
    <select id="queryItemConfigInfoList" resultType="com.ruoyi.raffle.domain.dto.LotteryItemConfigInfoDTO">
        SELECT
            t1.id,
            t1.activity_id activityId,
            t1.item_id itemId,
            t1.bind_mobile_flag bindMobileFlag,
            t1.bind_name_flag bindNameFlag,
            t1.mobile_unique_flag mobileUniqueFlag, 
            t2.type,
            t2.`name` name,
            t2.cover,
            t2.code_grant_type codeGrantType
        FROM
            lottery_item_config t1 left join lottery_item t2 on t1.item_id = t2.id
        WHERE
            t1.activity_id = #{activityId}
        order by t1.id asc

    </select>


</mapper>
