package com.ruoyi.web.convert.raffle;

import com.ruoyi.raffle.domain.ActivityMember;
import com.ruoyi.raffle.domain.ActivityMemberAddress;
import com.ruoyi.raffle.domain.dto.request.ActivityPageRequest;
import com.ruoyi.raffle.service.IActivityMemberAddressService;
import com.ruoyi.web.model.raffle.ActivityMemberAddressVO;
import com.ruoyi.web.model.raffle.ActivityMemberVO;
import com.ruoyi.web.model.raffle.ActivityPageQueryVO;
import org.apache.commons.collections.ArrayStack;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.Objects;

@Component
public class ActivityConverter {

    @Resource
    private IActivityMemberAddressService activityMemberAddressService;


    public ActivityMemberVO convert(ActivityMember member) {
        ActivityMemberVO activityMemberVO = new ActivityMemberVO();
        activityMemberVO.setId(member.getId());
        activityMemberVO.setUserId(member.getUserId());
        activityMemberVO.setRaffleCode(member.getRaffleCode());
        activityMemberVO.setActivityId(member.getActivityId());
        activityMemberVO.setActivityName(member.getActivityName());
        activityMemberVO.setActivityPrize(member.getActivityPrize());
        activityMemberVO.setWinTime(member.getWinTime());
        activityMemberVO.setPhone(member.getPhone());
        activityMemberVO.setNickName(member.getNickName());
        activityMemberVO.setAvatar(member.getAvatar());
        activityMemberVO.setRaffleStatus(member.getRaffleStatus());
        activityMemberVO.setExchangeStatus(member.getExchangeStatus());
        activityMemberVO.setStatus(member.getStatus());
        activityMemberVO.setTenantId(member.getTenantId());
        activityMemberVO.setCreateId(member.getCreateId());
        activityMemberVO.setCreateName(member.getCreateName());
        activityMemberVO.setCreateDate(member.getCreateDate());
        activityMemberVO.setUpdateId(member.getUpdateId());
        activityMemberVO.setUpdateName(member.getUpdateName());
        activityMemberVO.setUpdateDate(member.getUpdateDate());
        activityMemberVO.setIsDeleted(member.getIsDeleted());

        //  设置地址
        ActivityMemberAddress memberAddress = activityMemberAddressService.getByMemberId(member.getId());
        if (Objects.nonNull(memberAddress)) {
            activityMemberVO.setAddress(this.convert(memberAddress));
        }

        return activityMemberVO;
    }

    public ActivityMemberAddressVO convert(ActivityMemberAddress activityMemberAddress) {
        ActivityMemberAddressVO activityMemberAddressVO = new ActivityMemberAddressVO();
        activityMemberAddressVO.setReceiver(activityMemberAddress.getReceiver());
        activityMemberAddressVO.setPhone(activityMemberAddress.getPhone());
        activityMemberAddressVO.setArea(activityMemberAddress.getArea());
        activityMemberAddressVO.setAddress(activityMemberAddress.getAddress());
        return activityMemberAddressVO;
    }


    public ActivityPageRequest convert(ActivityPageQueryVO vo){
        ActivityPageRequest activityPageRequest = new ActivityPageRequest();
        activityPageRequest.setActivityName(vo.getActivityName());
        if (Objects.nonNull(vo.getStatus())) {
            activityPageRequest.setStatuses(Collections.singletonList(vo.getStatus()));
        }
        activityPageRequest.setCreateDateStart(vo.getCreateDateStart());
        activityPageRequest.setCreateDateEnd(vo.getCreateDateEnd());
        activityPageRequest.setRaffleOpenTimeStart(vo.getRaffleOpenTimeStart());
        activityPageRequest.setRaffleOpenTimeEnd(vo.getRaffleOpenTimeEnd());
        return activityPageRequest;

    }

}
