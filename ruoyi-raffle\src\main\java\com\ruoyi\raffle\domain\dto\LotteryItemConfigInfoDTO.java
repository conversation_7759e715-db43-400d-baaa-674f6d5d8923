package com.ruoyi.raffle.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/11/7 4:56 下午
 */

@Data
public class LotteryItemConfigInfoDTO {
    /**
     * 模板id
     */
    private Long id;

    /**
     * 活动id
     */
    private Long activityId;

    /**
     * 奖项id
     */
    private Long itemId;

    /**
     * 是否需要绑定手机号 0-不需要 1-需要 2-使用当前账户的手机号
     */
    private Integer bindMobileFlag;

    /**
     * 是否需要绑定姓名 0-不需要 1-需要
     */
    private Integer bindNameFlag;

    /**
     * 当需要绑定手机号时，手机号是否唯一： 0-不唯一 1-唯一
     */
    private Integer mobileUniqueFlag;


    /**
     * 类型: 1-现金 2-今豆 3-物品
     */
    private Integer type;

    /**
     * 奖项名称
     */
    private String name;

    /**
     * 奖项图片
     */
    private String cover;

    /**
     * 券码发放类型: 0-不发放 1-发放
     */
    private Integer codeGrantType;

    /**
     * 奖项图片url
     */
    private String coverUrl;

    /**
     * 券码数量
     */
    private Long codeAmount;

    /**
     * 券码发放数量
     */
    private Long codeGrantAmount;
    /**
     * 奖品数量
     */
    private Integer prizeAmount;

}
