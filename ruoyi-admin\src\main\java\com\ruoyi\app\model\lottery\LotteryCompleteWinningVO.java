
package com.ruoyi.app.model.lottery;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 用户抽奖
 * <AUTHOR>
 * @date 2024/1/30 11:04
 */
@Data
@ApiModel
public class LotteryCompleteWinningVO {

    @ApiModelProperty("活动ID")
    @NotNull(message = "活动ID不能为空")
    @Min(value=1,message = "活动ID必须大于0")
    @Max(value=99999999,message = "活动ID必须小于100000000")
    private Long  activityId;

    @ApiModelProperty("抽奖记录Id")
    @NotNull(message = "抽奖记录Id不能为空")
    private Long  logId;

    @ApiModelProperty("手机号")
    private String  mobile;

    @ApiModelProperty("姓名")
    private String  name;
}
