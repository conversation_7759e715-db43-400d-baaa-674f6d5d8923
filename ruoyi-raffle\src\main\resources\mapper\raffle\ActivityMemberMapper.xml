<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.raffle.mapper.ActivityMemberMapper">

    <update id="updateRaffleCode">

        UPDATE activity_member t1
            JOIN (
                SELECT
                    t.id,
                    @rownum := @rownum + 1 AS  new_row_number from
                 activity_member t, (SELECT @rownum := 0) r
                where t.activity_id = #{activityId}
                ORDER BY
                    t.create_date
            ) AS t2
            ON t1.id = t2.id
        SET t1.raffle_code = CONCAT(#{codePrefix} ,t2.new_row_number)
        where t1.activity_id =  #{activityId}

    </update>
</mapper>