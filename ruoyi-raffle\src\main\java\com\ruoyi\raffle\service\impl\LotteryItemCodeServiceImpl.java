package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.raffle.domain.LotteryItemCode;
import com.ruoyi.raffle.mapper.LotteryItemCodeMapper;
import com.ruoyi.raffle.service.ILotteryItemCodeService;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * 抽奖券码表 服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class LotteryItemCodeServiceImpl extends ServiceImpl<LotteryItemCodeMapper, LotteryItemCode> implements ILotteryItemCodeService {


}