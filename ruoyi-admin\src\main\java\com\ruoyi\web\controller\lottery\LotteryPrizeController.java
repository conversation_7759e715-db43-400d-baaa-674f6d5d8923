package com.ruoyi.web.controller.lottery;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;

import com.ruoyi.raffle.domain.LotteryPrize;
import com.ruoyi.raffle.service.ILotteryPrizeService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 抽奖奖品表 控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/lottery/prize")
public class LotteryPrizeController extends BaseController {

    @Resource
    private ILotteryPrizeService lotteryPrizeService;

    /**
     * 查询抽奖奖品列表
     */
    //@PreAuthorize("@ss.hasPermi('lottery:prize:list')")
    @GetMapping("/list")
    public TableDataInfo list(LotteryPrize lotteryPrize) {
        startPage();
        LambdaQueryWrapper<LotteryPrize> queryWrapper = new LambdaQueryWrapper<>();
        if (lotteryPrize.getActivityId() != null) {
            queryWrapper.eq(LotteryPrize::getActivityId, lotteryPrize.getActivityId());
        }
        if (lotteryPrize.getItemId() != null) {
            queryWrapper.eq(LotteryPrize::getItemId, lotteryPrize.getItemId());
        }
        if (lotteryPrize.getPrizeType() != null) {
            queryWrapper.eq(LotteryPrize::getPrizeType, lotteryPrize.getPrizeType());
        }
        queryWrapper.orderByAsc(LotteryPrize::getSort);
        List<LotteryPrize> list = lotteryPrizeService.list(queryWrapper);
        return getDataTable(list);
    }

    /**
     * 获取抽奖奖品详细信息
     */
    //@PreAuthorize("@ss.hasPermi('lottery:prize:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(lotteryPrizeService.getById(id));
    }

    /**
     * 新增抽奖奖品
     */
    //@PreAuthorize("@ss.hasPermi('lottery:prize:add')")
    @Log(title = "抽奖奖品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody LotteryPrize lotteryPrize) {
        return toAjax(lotteryPrizeService.save(lotteryPrize));
    }

    /**
     * 修改抽奖奖品
     */
    //@PreAuthorize("@ss.hasPermi('lottery:prize:edit')")
    @Log(title = "抽奖奖品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody LotteryPrize lotteryPrize) {
        return toAjax(lotteryPrizeService.updateById(lotteryPrize));
    }

    /**
     * 删除抽奖奖品
     */
    //@PreAuthorize("@ss.hasPermi('lottery:prize:remove')")
    @Log(title = "抽奖奖品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(lotteryPrizeService.removeByIds(Arrays.asList(ids)));
    }
}