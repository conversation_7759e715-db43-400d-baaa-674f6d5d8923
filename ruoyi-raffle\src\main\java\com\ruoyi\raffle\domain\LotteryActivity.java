package com.ruoyi.raffle.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 抽奖活动表实体类
 * 用于管理抽奖活动的基本信息和配置参数
 *
 * <AUTHOR>
 * @date 2023-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lottery_activity")
public class LotteryActivity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动主题
     */
    @TableField("title")
    private String title;

    /**
     * 活动开始时间
     */
    @TableField("start_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime startDate;

    /**
     * 活动结束时间
     */
    @TableField("end_date")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime endDate;

    /**
     * 活动简介
     */
    @TableField("introduction")
    private String introduction;

    /**
     * 活动状态：0-未上架、1-上架中、2-已下架
     */
    @TableField("status")
    private Integer status;

    /**
     * 活动类型 1-按库存抽奖 2-按概率抽奖
     */
    @TableField("type")
    private Integer type;

    /**
     * 删除标志  0-未删除 1-已删除
     */
    @TableField("del_flag")
    private Integer delFlag;

    /**
     * 比重
     */
    @TableField("weight")
    private Integer weight;

    /**
     * 每日能抽奖次数(或者每日登录奖励次数)
     */
    @TableField("lottery_amount")
    private Integer lotteryAmount;

    /**
     * 每日分享获得最大次数
     */
    @TableField("share_max_amount")
    private Integer shareMaxAmount;

    /**
     * 抽奖消耗今豆配置 0-不消耗 1-每次都消耗 2-每天第一次消耗
     */
    @TableField("deplete_bean_type")
    private Integer depleteBeanType;

    /**
     * 抽奖消耗今豆数
     */
    @TableField("deplete_bean")
    private Integer depleteBean;

    /**
     * 限制设备正常抽奖次数（每日）
     */
    @TableField("device_limit")
    private Integer deviceLimit;

    /**
     * 配置观看时长奖励抽奖次数（观看时长，秒）
     */
    @TableField("watch_duration")
    private Integer watchDuration;

    /**
     * 观看时长达标获取抽奖次数 系数
     */
    @TableField("watch_factor")
    private Integer watchFactor;

    /**
     * 邀请成功获取抽奖次数 系数
     */
    @TableField("invite_factor")
    private Integer inviteFactor;

    /**
     * 邀请成功人数奖励上限
     */
    @TableField("invite_max_amount")
    private Integer inviteMaxAmount;

    /**
     * 邀请成功奖励今豆数
     */
    @TableField("invite_reward_bean")
    private Integer inviteRewardBean;

    /**
     * 兜底今豆数-最小值
     */
    @TableField("backstop_bean_min")
    private Integer backstopBeanMin;

    /**
     * 兜底今豆数-最大值
     */
    @TableField("backstop_bean_max")
    private Integer backstopBeanMax;

    /**
     * 限制ip抽奖 0-不限制 1-限制
     */
    @TableField("limit_ip_addr")
    private Integer limitIpAddr;

    /**
     * 创建用户id
     */
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 创建用户账号
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 更新用户id
     */
    @TableField("update_user_id")
    private Long updateUserId;

    /**
     * 更新用户账号
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField("update_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;

    /**
     * 错误信息
     */
    @TableField(exist = false)
    private String errorMessage;
}