package com.ruoyi.raffle.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 抽奖奖品配置表实体类
 * 用于配置活动中奖项的绑定规则和限制策略
 * 
 * <AUTHOR>
 * @date 2023-12-19
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lottery_item_config")
public class LotteryItemConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动id
     */
    @TableField("activity_id")
    private Long activityId;

    /**
     * 奖项id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 是否需要绑定手机号 0-不需要 1-需要 2-使用当前账户的手机号
     */
    @TableField("bind_mobile_flag")
    private Integer bindMobileFlag;

    /**
     * 是否需要绑定姓名 0-不需要 1-需要
     */
    @TableField("bind_name_flag")
    private Integer bindNameFlag;

    /**
     * 当需要绑定手机号时，手机号是否唯一： 0-不唯一 1-唯一
     */
    @TableField("mobile_unique_flag")
    private Integer mobileUniqueFlag;

    /**
     * 创建用户id
     */
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 创建用户账号
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 更新用户id
     */
    @TableField("update_user_id")
    private Long updateUserId;

    /**
     * 更新用户账号
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField("update_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}
