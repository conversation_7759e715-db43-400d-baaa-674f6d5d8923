package com.ruoyi.raffle.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

/**
 * 活动参与成员收货地址
 *
 * <AUTHOR>
 * @date 2023/11/7 3:11 下午
 */

@Data
public class ActivityMemberAddress {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 活动报名ID
     */
    private Long memberId;
    /**
     * 收货人名称
     */
    private String receiver;

    /**
     * 收货人手机号
     */
    private String phone;

    /**
     * 所在地区
     */
    private String area;
    /**
     * 详细地址
     */
    private String address;
    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人id
     */
    private Long updateId;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 逻辑删除标记
     */
    private Boolean isDeleted;

}
