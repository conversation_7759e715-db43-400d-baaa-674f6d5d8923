package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.raffle.domain.ActivityMemberAddress;
import com.ruoyi.raffle.domain.dto.request.ActivityMemberEditAddressRequest;
import com.ruoyi.raffle.mapper.ActivityMemberAddressMapper;
import com.ruoyi.raffle.service.IActivityMemberAddressService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/11/6 4:58 下午
 */

@Service
public class ActivityMemberAddressServiceImpl extends ServiceImpl<ActivityMemberAddressMapper, ActivityMemberAddress> implements IActivityMemberAddressService {

    @Override
    public List<ActivityMemberAddress> listByCondition() {
        LambdaQueryWrapper<ActivityMemberAddress> queryWrapper = Wrappers.lambdaQuery();

        return this.list(queryWrapper);
    }

    @Override
    public boolean editAddress(ActivityMemberEditAddressRequest request) {
        LambdaQueryWrapper<ActivityMemberAddress> addressQuery = Wrappers.lambdaQuery();
        addressQuery.eq(ActivityMemberAddress::getMemberId, request.getMemberId());
        ActivityMemberAddress entity = this.getOne(addressQuery);
        if (Objects.nonNull(entity)) {
            entity.setReceiver(request.getReceiver());
            entity.setPhone(request.getPhone());
            entity.setArea(request.getArea());
            entity.setAddress(request.getAddress());

            entity.setUpdateId(request.getEditorId());
            entity.setUpdateDate(request.getEditDate());
            entity.setUpdateName(request.getEditorName());

            return this.updateById(entity);
        } else {
            entity = new ActivityMemberAddress();

            entity.setMemberId(request.getMemberId());
            entity.setReceiver(request.getReceiver());
            entity.setPhone(request.getPhone());
            entity.setArea(request.getArea());
            entity.setAddress(request.getAddress());

            entity.setTenantId(request.getTenantId());
            entity.setCreateId(request.getEditorId());
            entity.setCreateDate(request.getEditDate());
            entity.setCreateName(request.getEditorName());

            return this.save(entity);
        }
    }

    @Override
    public ActivityMemberAddress getByMemberId(Long memberId) {
        LambdaQueryWrapper<ActivityMemberAddress> addressQuery = Wrappers.lambdaQuery();
        addressQuery.eq(ActivityMemberAddress::getMemberId, memberId);

        return this.getOne(addressQuery);
    }


}
