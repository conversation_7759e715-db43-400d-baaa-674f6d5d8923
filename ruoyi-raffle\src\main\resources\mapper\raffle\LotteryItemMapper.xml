<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.raffle.mapper.LotteryItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ruoyi.raffle.domain.LotteryItem">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="name" property="name" />
        <result column="cover" property="cover" />
        <result column="prize_min" property="prizeMin" />
        <result column="prize_max" property="prizeMax" />
        <result column="code_grant_type" property="codeGrantType" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_date" property="createDate" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_date" property="updateDate" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, name, cover, prize_min, prize_max, code_grant_type,
        create_user_id, create_user_name, create_date,
        update_user_id, update_user_name, update_date
    </sql>

</mapper>