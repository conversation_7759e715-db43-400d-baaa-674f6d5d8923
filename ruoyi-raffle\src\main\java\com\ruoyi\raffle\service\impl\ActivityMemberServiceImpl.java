package com.ruoyi.raffle.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.user.BlackListException;
import com.ruoyi.common.exception.user.DuplicateApplyException;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.raffle.domain.Activity;
import com.ruoyi.raffle.domain.ActivityMember;
import com.ruoyi.raffle.domain.RaffleUser;
import com.ruoyi.raffle.domain.dto.request.ActivityApplyRequest;
import com.ruoyi.raffle.domain.dto.request.ActivityWinRequest;
import com.ruoyi.raffle.mapper.ActivityMemberMapper;
import com.ruoyi.raffle.service.IActivityMemberService;
import com.ruoyi.raffle.service.IActivityService;
import com.ruoyi.raffle.service.IRaffleUserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/11/6 4:58 下午
 */

@Service
public class ActivityMemberServiceImpl extends ServiceImpl<ActivityMemberMapper, ActivityMember> implements IActivityMemberService {


    @Resource
    private IActivityService activityService;
    @Resource
    private IRaffleUserService raffleUserService;

    @Override
    public List<ActivityMember> getWinMembers(ActivityWinRequest request) {
        //  有效且中奖的用户状态
        LambdaQueryWrapper<ActivityMember> memberQuery = Wrappers.lambdaQuery();
        memberQuery.eq(Objects.nonNull(request.getActivityId()), ActivityMember::getActivityId, request.getActivityId());
        memberQuery.eq(Objects.nonNull(request.getTenantId()), ActivityMember::getTenantId, request.getTenantId());
        memberQuery.eq(ActivityMember::getRaffleStatus, 1);
        memberQuery.eq(ActivityMember::getStatus, 1);
        //  按照中奖时间倒序排列
        memberQuery.orderByDesc(ActivityMember::getWinTime);

        return this.list(memberQuery);
    }

    @Override
    public Integer getApplyCount(Long activityId) {
        LambdaQueryWrapper<ActivityMember> memberQuery = Wrappers.lambdaQuery();
        memberQuery.eq(ActivityMember::getActivityId, activityId);
        memberQuery.eq(ActivityMember::getStatus, 1);

        return Math.toIntExact(this.count(memberQuery));
    }

    @Override
    public Long activityApply(ActivityApplyRequest request) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long activityId = request.getActivityId();
        Activity activity = activityService.getById(activityId);
        if (Objects.isNull(activity)) {
            throw new BlackListException();
        }

        RaffleUser raffleUser = raffleUserService.getBySysUserId(user.getUserId());


        //  检查用户是否重复报名
        ActivityMember member = this.getByActivityIdAndUserId(activityId, user.getUserId());
        if (Objects.nonNull(member)) {
            throw new DuplicateApplyException();
        }


        ActivityMember entity = new ActivityMember();
        entity.setActivityId(activityId);
        entity.setActivityName(activity.getActivityName());
        entity.setActivityPrize(activity.getActivityPrize());
        entity.setUserId(user.getUserId());
        entity.setNickName(raffleUser.getNickname());
        entity.setAvatar(raffleUser.getAvatar());
        entity.setTenantId(user.getTenantId());
        entity.setRaffleCode(genRaffleCode(activity));
        entity.setRaffleStatus(0);
        entity.setExchangeStatus(0);
        entity.setStatus(1);
        entity.setCreateId(user.getUserId());
        entity.setCreateDate(new Date());
        entity.setCreateName(user.getNickName());

        this.save(entity);

        return entity.getId();
    }

    @Override
    public ActivityMember getByActivityIdAndUserId(Long activityId, Long userId) {
        LambdaQueryWrapper<ActivityMember> memberQuery = Wrappers.lambdaQuery();
        memberQuery.eq(ActivityMember::getActivityId, activityId);
        memberQuery.eq(ActivityMember::getUserId, userId);

        return this.getOne(memberQuery);
    }

    @Override
    public Integer getRaffleStatus(Long activityId, Long userId) {
        ActivityMember one = getByActivityIdAndUserId(activityId, userId);
        return Optional.ofNullable(one).map(ActivityMember::getRaffleStatus).orElse(0);
    }

    @Override
    public boolean exchangeStatusChange(Long memberId, Integer status) {

        ActivityMember member = new ActivityMember();
        member.setId(memberId);
        member.setExchangeStatus(status);
        return this.updateById(member);
    }

    /**
     * 抽奖码格式为（渠道+活动期数+从0开始的递增数字）
     *
     * @param activity
     * @return
     */
    @Override
    public String genRaffleCode(Activity activity) {
        //  渠道
        String tenantId = activity.getTenantId();

        //  报名序号
        LambdaQueryWrapper<ActivityMember> applyQuery = Wrappers.lambdaQuery();
        applyQuery.eq(ActivityMember::getActivityId, activity.getId());
        long index = this.count(applyQuery) + 1;

        return tenantId + activity.getActivityNum() + index;
    }

    @Override
    public boolean updateRaffleCode(Long activityId, String codePrefix) {
        this.baseMapper.updateRaffleCode(activityId, codePrefix);
        return true;
    }
}
