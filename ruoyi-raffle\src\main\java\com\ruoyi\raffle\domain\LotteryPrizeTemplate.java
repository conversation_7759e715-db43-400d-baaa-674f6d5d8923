package com.ruoyi.raffle.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 抽奖奖品模板表实体类
 * 用于配置抽奖活动的奖品模板，支持复用和批量生成奖品配置
 * 
 * <AUTHOR>
 * @date 2023-12-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("lottery_prize_template")
public class LotteryPrizeTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动id
     */
    @TableField("activity_id")
    private Long activityId;

    /**
     * 奖项id
     */
    @TableField("item_id")
    private Long itemId;

    /**
     * 总数量
     */
    @TableField("amount")
    private Integer amount;

    /**
     * 奖品类型 0-优先 1-保底
     */
    @TableField("prize_type")
    private Integer prizeType;

    /**
     * 用户中奖策略：0-不限制 1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中
     */
    @TableField("user_strategy")
    private Integer userStrategy;

    /**
     * 设备中奖策略：0-不限制 1-一天只能中一次 2-整个活动区间只能中一次 3-抽不中
     */
    @TableField("device_strategy")
    private Integer deviceStrategy;

    /**
     * 互斥的奖项id集合
     */
    @TableField("mutually_item_ids")
    private String mutuallyItemIds;

    /**
     * 顺序
     */
    @TableField("sort")
    private Integer sort;

    /**
     * 创建用户id
     */
    @TableField("create_user_id")
    private Long createUserId;

    /**
     * 创建用户账号
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createDate;

    /**
     * 更新用户id
     */
    @TableField("update_user_id")
    private Long updateUserId;

    /**
     * 更新用户账号
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 更新时间
     */
    @TableField("update_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateDate;
}
