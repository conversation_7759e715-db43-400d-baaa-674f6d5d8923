package com.ruoyi.web.model.raffle;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ActivityVO implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动规则
     */
    private String activityRule;

    /**
     * 活动介绍
     */
    private String activityDescription;

    /**
     * 活动期数
     */
    private Integer activityNum;

    /**
     * 开奖时间
     */
    private Date raffleOpenTime;

    /**
     * 活动宣传图
     */
    private String activityPromoPic;
    /**
     * 活动宣传图URL
     */
    private String activityPromoPicUrl;

    /**
     * 背景颜色
     */
    private String bgColor;
    /**
     * 是否为深色字体
     */
    private Boolean isDarkFont;
    /**
     * 活动奖品名称
     */
    private String activityPrize;

    /**
     * 活动奖品数量
     */
    private Integer activityPrizeNum;

    /**
     * 活动奖品价值
     */
    private String activityPrizeValue;

    /**
     * 活动奖品图片
     */
    private String activityPrizePic;
    /**
     * 活动奖品图片URL
     */
    private String activityPrizePicUrl;
    /**
     * 奖品是否需要寄送
     */
    private Integer activityPrizeNeedSent;

    /**
     * 活动链接
     */
    private String activityUrl;

    /**
     * 活动状态：0-待上架、1-活动中，2-已结束
     */
    private Integer status;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 创建人id
     */
    private Long createId;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人id
     */
    private Long updateId;

    /**
     * 更新人
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 逻辑删除标记
     */
    private Boolean isDeleted;





}
