package com.ruoyi.common.handler;

import com.baomidou.mybatisplus.core.parser.ISqlParser;
import com.baomidou.mybatisplus.core.parser.SqlInfo;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/3 9:31 上午
 */
@Component
public class MyTenantSqlParser implements ISqlParser {
    @Override
    public SqlInfo parser(MetaObject metaObject, String sql) {
        // 从当前线程、请求头或其他途径获取租户ID
        Long tenantId = 1L; // 示例：获取租户ID的逻辑
        if (tenantId != null) {
            // 根据需要过滤不需要进行多租户操作的表
            List<String> filterTables = Arrays.asList("activety", "table2", "table3");
            for (String table : filterTables) {
                if (sql.contains(table)) {
                    return null; // 返回null表示过滤该表
                }
            }
            // 添加租户ID条件
            sql += " AND tenant_id = " + tenantId;
        }
        return SqlInfo.newInstance().setSql(sql);
    }
}
