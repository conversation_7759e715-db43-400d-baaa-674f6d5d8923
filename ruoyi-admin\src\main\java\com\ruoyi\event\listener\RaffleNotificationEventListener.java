package com.ruoyi.event.listener;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.event.RaffleNotificationEvent;
import com.ruoyi.raffle.domain.Activity;
import com.ruoyi.raffle.domain.ActivityMember;
import com.ruoyi.raffle.domain.RaffleUser;
import com.ruoyi.raffle.service.IActivityMemberService;
import com.ruoyi.raffle.service.IActivityService;
import com.ruoyi.raffle.service.IRaffleUserService;
import com.ruoyi.system.domain.SysTenant;
import com.ruoyi.system.service.ISysTenantService;
import com.ruoyi.third.party.domain.dto.request.jsp.JspNotificationRequest;
import com.ruoyi.third.party.domain.dto.response.AppletRouteQueryResponse;
import com.ruoyi.third.party.domain.dto.response.jsp.JspNotificationResponse;
import com.ruoyi.third.party.service.IAppletRouteService;
import com.ruoyi.third.party.service.IJspService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RaffleNotificationEventListener {

    @Resource
    private IActivityMemberService activityMemberService;
    @Resource
    private IRaffleUserService raffleUserService;
    @Resource
    private ISysTenantService sysTenantService;
    @Resource
    private IActivityService activityService;
    @Resource
    private IAppletRouteService appletRouteService;

    @Resource
    private IJspService jspService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Async
    @EventListener
    public void onEvent(RaffleNotificationEvent event) {
        log.info("接收中奖通知事件：{}", JSON.toJSONString(event));

        Long memberId = event.getMemberId();
        //  活动报名信息
        ActivityMember member = activityMemberService.getById(memberId);
        //  租户信息
        SysTenant sysTenant = sysTenantService.getById(member.getTenantId());
        //  加锁避免发送重复消息
        Boolean getLock = stringRedisTemplate.opsForValue().setIfAbsent("raffle-notice::" + memberId, "1", 1, TimeUnit.DAYS);
        if (Boolean.TRUE.equals(getLock)) {
            //  根据不同平台，分发推送信息
            if (Objects.equals(sysTenant.getPlatform(), 1)) {
                //  1、今视频平台
                sendNotificationToJsp(member);
            } else {
                //  2、赣云平台

            }
        }
    }

    /**
     * 给今视频发送通知
     *
     * @param member 通知事件
     */
    private void sendNotificationToJsp(ActivityMember member) {
        //  抽奖用户信息
        RaffleUser raffleUser = raffleUserService.getBySysUserId(member.getUserId());
        //  活动详情
        Activity activity = activityService.getById(member.getActivityId());
        //  小程序跳转URL
        AppletRouteQueryResponse.DataDTO appletInfo = getAppletInfo(activity.getId(), activity.getTenantId());
        Map<String, Object> extra = new HashMap<>();
        if (Objects.nonNull(appletInfo) && Objects.nonNull(appletInfo.getApplet())) {
            extra.put("appletName", appletInfo.getApplet().getName());
            extra.put("appletIconUrl", appletInfo.getApplet().getIcon());
        }
        extra.put("messageType", "开奖提醒");
        extra.put("buttonText", "点击查看");

        List<Map<String, Object>> content = new ArrayList<>();
        Map<String, Object> activityName = new HashMap<>();
        activityName.put("key", "活动名称");
        activityName.put("value", activity.getActivityName());
        content.add(activityName);

        Map<String, Object> raffleOpenTime = new HashMap<>();
        raffleOpenTime.put("key", "开奖时间");
        raffleOpenTime.put("value", activity.getRaffleOpenTime());
        content.add(raffleOpenTime);

        Map<String, Object> activityPrizeName = new HashMap<>();
        activityPrizeName.put("key", "奖品名称");
        activityPrizeName.put("value", activity.getActivityPrize());
        content.add(activityPrizeName);

        extra.put("contents", content);

        JspNotificationRequest request = new JspNotificationRequest();
        request.setFromType(0);
        request.setFromId(0L);
        request.setFromName("小程序平台");
        request.setToType(2);
        request.setToIds(Collections.singletonList(Long.valueOf(raffleUser.getOpenId())));
        request.setTitle("小程序");
        request.setType(2);
        request.setIntro(activity.getActivityName());
        request.setDetail(activity.getActivityDescription());
        if (Objects.nonNull(appletInfo)) {
            List<AppletRouteQueryResponse.DataDTO.UrlListDTO> urlList = appletInfo.getUrlList();
            if (Objects.nonNull(urlList)) {
                for (AppletRouteQueryResponse.DataDTO.UrlListDTO urlListDTO : urlList) {
                    if (Objects.equals(activity.getTenantId(), urlListDTO.getPlatformId())) {
                        request.setOuterUrl(addScene(urlListDTO.getUrl()));
                    }
                }
            }
        }
        request.setExtra(JSON.toJSONString(extra));
        request.setAdminUser("18888888888");

        JspNotificationResponse jspNotificationResponse = jspService.sendNotification(request);
        log.info("今视频通知结果:{}", JSON.toJSONString(jspNotificationResponse));
    }


    private String addScene(String url) {
        return StringUtils.hasText(url) ? url + "&scene=103" : url;
    }


    private AppletRouteQueryResponse.DataDTO getAppletInfo(Long activityId, String tenantId) {
        AppletRouteQueryResponse response = appletRouteService.getRouteList(activityId, "pages/index/index");
        if (Objects.nonNull(response)) {
            return response.getData();
        }
        return null;
    }

}
